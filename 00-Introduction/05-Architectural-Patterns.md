
# Architectural Patterns

**Skill Level: Intermediate to Advanced**

## Notes

This comprehensive collection provides definitions and examples of architectural patterns used in modern software systems, particularly in cloud-native and distributed environments. Each pattern includes a description, key characteristics, and example implementations to help you understand when and how to apply these patterns in your architectural designs.

For related terminology and concepts, see the [Technical Terminology Glossary](04-Technical-Terminology-Glossary.md).

## Architecture Patterns and Concepts

### Strangler Fig Pattern

The Strangler Fig Pattern (also called Strangler Application Pattern) is an incremental approach to replacing a legacy system with a new system. Named after the strangler fig vines that grow around trees in rainforests, this pattern involves:

1. Creating a facade or interface layer around the legacy system
2. Gradually building new functionality as microservices or components
3. Incrementally routing requests from the legacy system to the new system
4. Eventually "strangling" (completely replacing) the legacy system

**Examples**:

1. **E-commerce Platform Migration**:
   - A retail company with a monolithic e-commerce platform creates an API gateway in front of the monolith
   - They build a new product catalog service and route those requests to it
   - Gradually, they build and migrate other services (checkout, user management, etc.)
   - Eventually, they decommission the original monolith when all functionality has been migrated

2. **Banking System Modernization**:
   - A bank with a legacy core banking system implements a service facade around it
   - They develop a new customer onboarding microservice and route new customer registrations to it
   - Next, they build a modern transaction processing service while maintaining the legacy system for existing accounts
   - Over 2-3 years, they incrementally replace all functionality and retire the legacy system

3. **Healthcare Records System**:
   - A hospital with an outdated patient records system creates an abstraction layer
   - They implement a new appointment scheduling module first
   - Then they migrate lab results and medication management
   - Patient demographic data is migrated last, completing the transition

### Polyglot Persistence

Polyglot persistence refers to using different data storage technologies for different types of data or use cases within a single application or system. Rather than forcing all data into a single database type, this approach selects the most appropriate database technology for each specific data requirement.

**Types of databases commonly used in polyglot persistence**:
- Relational databases (PostgreSQL, MySQL) for structured data with complex relationships
- Document databases (MongoDB, Couchbase) for semi-structured data
- Key-value stores (Redis, DynamoDB) for simple, high-throughput data access
- Graph databases (Neo4j) for highly connected data
- Time-series databases (InfluxDB, Prometheus) for metrics and monitoring data
- Search engines (Elasticsearch) for full-text search capabilities

**Examples**:

1. **Social Media Platform**:
   - PostgreSQL for user profiles and relationships
   - Redis for session management and caching
   - MongoDB for storing user-generated content
   - Elasticsearch for search functionality
   - Neo4j for analyzing connection networks and recommendations

2. **E-commerce System**:
   - MySQL for product catalog and order management
   - Redis for shopping cart and session data
   - Elasticsearch for product search
   - InfluxDB for monitoring and metrics
   - Cassandra for high-volume transaction logs

3. **Financial Services Application**:
   - PostgreSQL for account information and transactions
   - Redis for real-time fraud detection
   - MongoDB for customer interaction history
   - Timescale DB for time-series market data
   - Elasticsearch for compliance document search

### CQRS (Command Query Responsibility Segregation)

CQRS is an architectural pattern that separates read and write operations for a data store. Instead of a single model to handle both commands (writes) and queries (reads), CQRS uses separate models:

1. **Command Model**: Handles create, update, and delete operations
2. **Query Model**: Optimized for read operations and reporting

This pattern is often used with Event Sourcing and is particularly useful for complex domains with high-performance requirements.

**Examples**:

1. **E-commerce Platform**:
   - Command model: Normalized relational database for processing orders and inventory updates
   - Query model: Denormalized document database for product searches and catalog browsing
   - Synchronization: Events published to a message queue update the read model when the write model changes

2. **Financial Trading System**:
   - Command model: Transactional database for executing trades and updating positions
   - Query model: In-memory data grid for real-time portfolio analysis and reporting
   - Synchronization: Change data capture (CDC) to propagate updates to the read model

3. **Content Management System**:
   - Command model: Relational database for content creation and editing workflows
   - Query model: Search engine for content discovery and retrieval
   - Synchronization: Scheduled batch processes to rebuild search indexes from the primary database

### Event Sourcing

Event Sourcing is a pattern where application state changes are stored as a sequence of events. Instead of storing just the current state, the system records each state change as an immutable event. The current state can be reconstructed by replaying these events.

**Benefits**:
- Complete audit trail and history
- Ability to reconstruct past states
- Temporal querying (what was the state at a specific time)
- Natural fit for event-driven architectures

**Examples**:

1. **Banking System**:
   - Events: Deposits, withdrawals, transfers, interest accruals
   - Implementation: Each account's transaction history is stored as an immutable log
   - Current state: Account balance calculated by replaying all events
   - Benefits: Complete audit trail, ability to reconstruct account state at any point in time

2. **Inventory Management**:
   - Events: Stock receipts, sales, returns, adjustments, transfers between locations
   - Implementation: Event log for each SKU with snapshots at intervals
   - Current state: Available quantity derived from events with occasional snapshots for performance
   - Benefits: Full traceability of inventory movements, accurate historical reporting

3. **Version Control System**:
   - Events: Commits, merges, branches, tags
   - Implementation: Git stores a chain of commits (events) rather than file differences
   - Current state: Working directory constructed by applying the sequence of changes
   - Benefits: Complete history, ability to revert to any previous state

### Saga Pattern

The Saga Pattern is a way to manage distributed transactions in microservices architectures. Instead of using a traditional two-phase commit across services, a saga is a sequence of local transactions where each transaction updates a single service and publishes an event to trigger the next transaction.

**Two main implementation approaches**:
1. **Choreography**: Each service produces and listens to events and decides what to do next
2. **Orchestration**: A central coordinator directs the steps of the saga

**Examples**:

1. **E-commerce Order Processing**:
   - Step 1: Create order (Order Service)
   - Step 2: Reserve inventory (Inventory Service)
   - Step 3: Process payment (Payment Service)
   - Step 4: Ship order (Shipping Service)
   - Compensation: If payment fails, inventory reservation is released; if shipping fails, payment is refunded and inventory released

2. **Travel Booking System**:
   - Step 1: Reserve flight (Flight Service)
   - Step 2: Book hotel (Hotel Service)
   - Step 3: Reserve car rental (Car Rental Service)
   - Step 4: Process payment (Payment Service)
   - Step 5: Confirm all reservations (Confirmation Service)
   - Compensation: If any step fails, all previous reservations are cancelled

3. **Banking Funds Transfer**:
   - Step 1: Verify sufficient funds (Account Service)
   - Step 2: Place hold on sender's account (Account Service)
   - Step 3: Credit receiver's account (Account Service)
   - Step 4: Debit sender's account and remove hold (Account Service)
   - Step 5: Record transaction (Ledger Service)
   - Compensation: If crediting fails, the hold is released; if debiting fails, a correcting credit is applied

### Circuit Breaker Pattern

The Circuit Breaker Pattern prevents cascading failures in distributed systems by "breaking the circuit" when a service is failing. It works like an electrical circuit breaker:

1. **Closed State**: Requests flow normally to the service
2. **Open State**: When failures exceed a threshold, the circuit opens and requests fail fast
3. **Half-Open State**: After a timeout, the circuit allows some requests through to test if the service has recovered

**Examples**:

1. **Payment Processing Service**:
   - Implementation: Circuit breaker monitors calls to payment gateway
   - Threshold: Opens after 5 consecutive failures or >20% error rate in 30-second window
   - Fallback: Return cached authorization response or queue for later processing
   - Recovery: After 60 seconds, allows 2 test requests per second to check if service recovered

2. **External API Integration**:
   - Implementation: Circuit breaker in API gateway for each downstream service
   - Threshold: Opens when latency exceeds 2 seconds for 10 consecutive requests
   - Fallback: Serve cached data with cache-status header or simplified response
   - Recovery: Exponential backoff starting at 5 seconds, doubling with each failed recovery attempt

3. **Database Access Layer**:
   - Implementation: Circuit breaker between application and database
   - Threshold: Opens when connection pool reaches 90% capacity or query timeouts exceed 10%
   - Fallback: Serve from read replica or in-memory cache
   - Recovery: Health check database connectivity every 15 seconds before allowing traffic

### Sidecar Pattern

The Sidecar Pattern deploys helper components as separate containers alongside the main application container, sharing the same lifecycle, resources, and network namespace. This pattern is fundamental to service mesh implementations.

**Common sidecar use cases**:
- Logging and monitoring agents
- Service mesh proxies (e.g., Envoy)
- Security and authentication components
- Configuration management

**Examples**:

1. **Service Mesh Implementation**:
   - Primary container: Business application (e.g., Java microservice)
   - Sidecar: Envoy proxy that intercepts all network traffic
   - Benefits: Traffic management, security, and observability without changing application code
   - Implementation: In Kubernetes with Istio, each application pod automatically gets an Envoy proxy sidecar

2. **Logging and Monitoring**:
   - Primary container: Application server (e.g., Node.js web service)
   - Sidecar: Fluent Bit container collecting and forwarding logs
   - Benefits: Centralized log collection without application modifications
   - Implementation: DaemonSet in Kubernetes that injects logging sidecars into application pods

3. **Legacy Application Modernization**:
   - Primary container: Legacy application with no built-in API capabilities
   - Sidecar: API adapter that exposes RESTful endpoints and translates to legacy protocols
   - Benefits: Modern API interface without modifying legacy code
   - Implementation: Ambassador container pattern with shared volumes for communication


## Additional Architectural Patterns

### Choreography Pattern

The Choreography Pattern is a distributed system interaction style where each component independently observes and reacts to events, without a central coordinator.

**Key characteristics**:
- Components react to events from other components
- No central orchestrator or coordinator
- Loose coupling between components
- Event-driven communication
- Highly autonomous components

**Examples**:

1. **E-commerce Order Processing**:
   - Order Service publishes "OrderCreated" event
   - Inventory Service subscribes, reserves items, publishes "InventoryReserved"
   - Payment Service subscribes, processes payment, publishes "PaymentProcessed"
   - Shipping Service subscribes, creates shipping label, publishes "OrderShipped"
   - Each service acts independently based on events it receives

2. **IoT Home Automation**:
   - Motion Sensor publishes "MotionDetected" event
   - Lighting Service subscribes and turns on lights
   - Security Service subscribes and activates camera recording
   - Notification Service subscribes and sends alert to homeowner
   - Each component reacts independently without central coordination

3. **Financial Transaction Processing**:
   - Transaction Service publishes "TransactionCreated" event
   - Fraud Detection Service subscribes and performs risk analysis
   - Accounting Service subscribes and records the transaction
   - Notification Service subscribes and alerts the customer
   - Analytics Service subscribes and updates dashboards
   - Each service performs its function without being directed

4. **Healthcare Patient Management**:
   - Admission Service publishes "PatientAdmitted" event
   - Room Assignment Service subscribes and allocates a room
   - Billing Service subscribes and initiates insurance verification
   - Nursing Service subscribes and adds patient to care schedule
   - Dietary Service subscribes and prepares meal requirements
   - Each department responds to the event according to their responsibilities

### Orchestration Pattern

The Orchestration Pattern is a distributed system interaction style where a central coordinator (orchestrator) controls the interaction between components, making decisions and directing the flow.

**Key characteristics**:
- Central orchestrator controls the process flow
- Components perform tasks as directed
- Orchestrator maintains state of the process
- Clear visibility of the entire process
- Centralized error handling

**Examples**:

1. **Order Processing Workflow**:
   - Order Orchestrator service coordinates the entire process
   - First calls Inventory Service to check and reserve stock
   - Then calls Payment Service to process payment
   - If payment succeeds, calls Shipping Service to arrange delivery
   - If any step fails, orchestrator executes compensation actions
   - Orchestrator maintains the state of the entire process

2. **Employee Onboarding**:
   - Onboarding Orchestrator manages the entire employee setup
   - Calls HR Service to create employee record
   - Calls IT Service to provision accounts and equipment
   - Calls Facilities Service to assign workspace
   - Calls Training Service to schedule required training
   - Orchestrator tracks progress and ensures all steps complete

3. **Insurance Claims Processing**:
   - Claims Orchestrator manages the claim lifecycle
   - Calls Validation Service to verify claim details
   - Calls Risk Assessment Service to evaluate the claim
   - Calls Adjudication Service to determine coverage
   - Calls Payment Service to issue payment if approved
   - Orchestrator handles timeouts, retries, and escalations

4. **Mortgage Application**:
   - Mortgage Orchestrator coordinates the approval process
   - Calls Credit Check Service to verify applicant's credit
   - Calls Property Valuation Service to assess the property
   - Calls Underwriting Service to evaluate risk
   - Calls Document Generation Service to prepare contracts
   - Orchestrator ensures regulatory compliance throughout the process

### Scatter-Gather Pattern

The Scatter-Gather Pattern involves broadcasting a request to multiple recipients and then aggregating their responses, useful for parallel processing and collecting comprehensive results.

**Key components**:
- Scatter phase: Request is sent to multiple recipients
- Processing phase: Recipients process the request independently
- Gather phase: Responses are collected and aggregated
- Aggregation logic: Combines responses (e.g., merge, select best, etc.)

**Examples**:

1. **Search Engine Results**:
   - Query is scattered to multiple specialized services (web, images, news, videos)
   - Each service processes the query independently and in parallel
   - Results are gathered from all services
   - Aggregator combines and ranks results from different sources
   - Combined results are presented to the user in a unified interface

2. **Price Comparison Service**:
   - Request is scattered to multiple e-commerce APIs and vendor services
   - Each service returns pricing and availability for the requested product
   - Results are gathered and normalized to a common format
   - Aggregator sorts by price, availability, and vendor rating
   - User receives comprehensive comparison from multiple sources

3. **Distributed Data Processing**:
   - Processing task is scattered to multiple worker nodes in a cluster
   - Each node processes a portion of the data independently
   - Results are gathered from all nodes
   - Aggregator combines partial results into the final output
   - System handles node failures by redistributing work

4. **Multi-Region Availability Check**:
   - Health check request is scattered to services in different geographic regions
   - Each regional instance reports its status and performance metrics
   - Results are gathered from all regions
   - Aggregator determines overall system health and identifies regional issues
   - Dashboard displays global and region-specific availability

### Throttling Pattern

The Throttling Pattern limits the rate at which a system or component can be used to prevent overload, maintain service quality, or enforce usage policies.

**Implementation approaches**:
- Rate limiting: Restricting requests per time period
- Concurrency limiting: Restricting simultaneous operations
- Token bucket: Allowing bursts within overall limits
- Adaptive throttling: Adjusting limits based on system conditions

**Examples**:

1. **API Rate Limiting**:
   - API gateway limits each client to 100 requests per minute
   - Excess requests receive a 429 Too Many Requests status
   - Premium clients receive higher limits than free tier users
   - Limits are enforced using token bucket algorithm allowing occasional bursts
   - Headers inform clients of remaining quota and reset time

2. **Database Connection Throttling**:
   - Connection pool limits concurrent database connections to 200
   - Requests beyond limit are queued with a 30-second timeout
   - Critical services receive dedicated connection allocation
   - System degrades gracefully under load rather than crashing
   - Monitoring alerts when connection usage exceeds 80%

3. **Email Sending Service**:
   - System limits outgoing emails to 50 per minute per customer
   - Marketing campaigns are throttled more aggressively than transactional emails
   - Excess emails are queued and sent when capacity becomes available
   - Anti-spam measures add additional throttling for suspicious patterns
   - Customers can purchase higher sending limits

4. **Resource-Intensive Operations**:
   - Report generation limited to 5 concurrent executions
   - Large file uploads throttled to 10 per minute per user
   - Background processing jobs limited based on server capacity
   - Adaptive throttling reduces limits during peak hours
   - User interface shows estimated wait time when throttled

### Circuit Breaker Pattern (Duplicate)

This pattern is described in detail earlier in the document. Please refer to the previous Circuit Breaker Pattern section for a complete description and examples.

### Microservices Architecture

Microservices architecture is an approach to developing a single application as a suite of small, independently deployable services, each running in its own process and communicating with lightweight mechanisms.

**Key characteristics**:
- Services are organized around business capabilities
- Each service has a single responsibility
- Services are independently deployable
- Services are loosely coupled
- Each service can use different technologies and data stores
- Services communicate via well-defined APIs

**Examples**:

1. **E-commerce Platform**:
   - User Management Service: Handles user accounts, authentication, and profiles
   - Product Catalog Service: Manages product information, categories, and search
   - Order Processing Service: Handles order creation and management
   - Payment Service: Processes payments and manages payment methods
   - Shipping Service: Handles shipping options, rates, and tracking
   - Each service has its own database and deployment pipeline

2. **Banking System**:
   - Account Service: Manages customer accounts and balances
   - Transaction Service: Processes deposits, withdrawals, and transfers
   - Authentication Service: Handles login and security
   - Notification Service: Sends alerts and statements to customers
   - Reporting Service: Generates financial reports and analytics
   - Each service can be scaled and deployed independently

3. **Streaming Media Platform**:
   - User Profile Service: Manages user accounts and preferences
   - Content Catalog Service: Maintains media metadata and categories
   - Recommendation Service: Generates personalized content suggestions
   - Streaming Service: Handles media delivery and playback
   - Billing Service: Manages subscriptions and payments
   - Each service uses technology optimized for its specific requirements

4. **Healthcare System**:
   - Patient Service: Manages patient information and medical history
   - Appointment Service: Handles scheduling and reminders
   - Billing Service: Processes insurance claims and payments
   - Pharmacy Service: Manages prescriptions and medication
   - Provider Service: Maintains information about doctors and specialists
   - Each service complies with relevant regulations while remaining independently deployable

### API Gateway Pattern

The API Gateway pattern provides a single entry point for clients to interact with a set of microservices. It acts as a reverse proxy, routing requests to appropriate services while handling cross-cutting concerns.

**Key responsibilities**:
- Request routing
- API composition
- Protocol translation
- Authentication and authorization
- Rate limiting and throttling
- Caching
- Monitoring and analytics

**Examples**:

1. **Mobile App Backend**:
   - API Gateway authenticates all incoming requests from mobile clients
   - Routes user profile requests to the User Service
   - Routes product browsing requests to the Product Catalog Service
   - Routes order placement to the Order Service
   - Handles rate limiting to prevent abuse
   - Transforms responses to optimize for mobile bandwidth constraints

2. **Public Developer Platform**:
   - API Gateway provides a unified entry point for third-party developers
   - Handles API key validation and quota enforcement
   - Routes requests to appropriate internal microservices
   - Provides consistent error handling and response formats
   - Logs all access for security and analytics
   - Shields internal architecture changes from external developers

3. **Multi-channel Retail System**:
   - API Gateway serves web, mobile, and kiosk interfaces
   - Provides channel-specific transformations and optimizations
   - Routes inventory queries to Inventory Service
   - Routes customer data to Customer Service
   - Handles authentication differently based on channel
   - Provides consistent monitoring across all access points

4. **B2B Integration Platform**:
   - API Gateway manages connections with partner systems
   - Handles protocol translation (SOAP to REST, etc.)
   - Routes partner-specific requests to appropriate services
   - Enforces partner-specific access controls and rate limits
   - Provides detailed audit logging for compliance
   - Handles retries and circuit breaking for reliability

### Backend for Frontend (BFF) Pattern

The Backend for Frontend pattern creates specialized backend services for specific frontend applications or interfaces, tailored to their unique requirements.

**Benefits**:
- Optimized for specific frontend needs
- Reduced data transfer
- Simplified frontend logic
- Independent evolution of different frontends

**Examples**:

1. **E-commerce Platform**:
   - Web BFF: Optimized for desktop browsers with rich product details and analytics
   - Mobile BFF: Streamlined responses with reduced payload size for mobile networks
   - Partner BFF: Specialized endpoints for marketplace sellers and affiliates
   - IoT BFF: Simplified interfaces for smart home devices and voice assistants
   - Each BFF communicates with the same core microservices but tailors the interface

2. **Banking Application**:
   - Consumer Web BFF: Full-featured interface for personal banking customers
   - Mobile Banking BFF: Optimized for common mobile tasks with reduced data
   - Wealth Management BFF: Specialized for investment advisors with detailed analytics
   - ATM BFF: Simplified interface for ATM interactions
   - Each BFF implements security appropriate to its channel

3. **Media Streaming Service**:
   - Smart TV BFF: Optimized for large screen browsing and high-bandwidth streaming
   - Mobile BFF: Supports offline viewing and bandwidth-efficient streaming
   - Gaming Console BFF: Tailored for controller-based navigation
   - Web BFF: Supports social sharing and detailed content information
   - Each BFF aggregates data differently based on device capabilities

4. **Enterprise SaaS Platform**:
   - Admin Portal BFF: Comprehensive management capabilities
   - Employee Dashboard BFF: Simplified day-to-day operational interface
   - Mobile Field Service BFF: Optimized for field workers with offline capabilities
   - Analytics BFF: Data-intensive reporting and visualization endpoints
   - Each BFF implements appropriate caching strategies for its use case

### Bulkhead Pattern

Named after compartments in a ship's hull, the Bulkhead pattern isolates components to prevent failures from cascading through the system. If one component fails, others continue to function.

**Implementation approaches**:
- Separate thread pools
- Separate processes
- Separate services
- Separate clusters or regions

**Examples**:

1. **Payment Processing System**:
   - Separate connection pools for different payment providers (Visa, PayPal, Stripe)
   - Isolated thread pools for processing different payment types
   - Resource limits applied independently to each integration
   - If one provider experiences issues, others continue to function normally
   - System can still process most payments even if one provider is down

2. **Cloud-based Application**:
   - Application deployed across multiple availability zones
   - Database replicated across regions with independent failover
   - Stateless services deployed in isolated clusters
   - Traffic routing configured to bypass failing components
   - System remains partially operational even during regional outages

3. **Microservices Platform**:
   - Services deployed in separate containers with resource limits
   - Independent scaling for each service based on its load
   - Circuit breakers implemented between service dependencies
   - Dedicated persistence stores for critical services
   - Failure in non-critical services doesn't affect core functionality

4. **Multi-tenant SaaS Application**:
   - Tenant data isolated in separate database schemas
   - Processing for premium customers runs on dedicated infrastructure
   - Background jobs separated from interactive request processing
   - Resource quotas enforced per tenant
   - Issues with one tenant don't impact service for others

### Domain-Driven Design (DDD)

Domain-Driven Design is an approach to software development that focuses on understanding and modeling the business domain, creating a shared language between developers and domain experts.

**Key concepts**:
- Ubiquitous Language: Common vocabulary shared by all team members
- Bounded Contexts: Explicit boundaries where models apply
- Context Mapping: Relationships between bounded contexts
- Aggregates: Clusters of domain objects treated as a unit
- Domain Events: Significant occurrences within the domain

**Examples**:

1. **Insurance System**:
   - Policy Management bounded context: Handles policy creation, updates, and renewals
   - Claims Processing bounded context: Manages claim submission, evaluation, and settlement
   - Customer Management bounded context: Maintains customer profiles and relationships
   - Risk Assessment bounded context: Evaluates insurance risks and determines premiums
   - Each context has its own domain model and ubiquitous language

2. **E-commerce Platform**:
   - Catalog bounded context: Products, categories, and inventory
   - Order bounded context: Shopping cart, checkout, and order fulfillment
   - Customer bounded context: User profiles, preferences, and history
   - Marketing bounded context: Promotions, recommendations, and campaigns
   - Each context is owned by a specific team with domain expertise

3. **Banking System**:
   - Account bounded context: Checking, savings, and account management
   - Lending bounded context: Loans, credit applications, and repayments
   - Investment bounded context: Securities, portfolios, and trading
   - Compliance bounded context: Regulatory requirements and reporting
   - Each context uses terminology specific to its financial domain

4. **Healthcare System**:
   - Patient Care bounded context: Medical records and treatment plans
   - Scheduling bounded context: Appointments and resource allocation
   - Billing bounded context: Insurance claims and patient payments
   - Pharmacy bounded context: Medications and prescriptions
   - Each context aligns with specific healthcare workflows and regulations

### Hexagonal Architecture (Ports and Adapters)

Hexagonal Architecture isolates the core business logic from external concerns by defining ports (interfaces) and adapters (implementations) that connect the application to external systems.

**Key components**:
- Core domain: Business logic at the center
- Ports: Interfaces defining how the core interacts with the outside
- Adapters: Implementations of ports connecting to specific technologies
- Driving adapters: Initiate interactions (UI, API)
- Driven adapters: Respond to the application (database, messaging)

**Examples**:

1. **Payment Processing Service**:
   - Core domain: Payment processing logic and business rules
   - Ports: PaymentGateway interface, TransactionRepository interface
   - Driving adapters: REST API, GraphQL API, message queue consumers
   - Driven adapters: Stripe adapter, PayPal adapter, database repositories
   - The core business logic remains unchanged when switching payment providers

2. **Inventory Management System**:
   - Core domain: Inventory allocation and tracking logic
   - Ports: InventoryRepository, NotificationService, SupplierService interfaces
   - Driving adapters: Web UI, mobile app API, scheduled jobs
   - Driven adapters: SQL database, email service, SMS service, ERP integration
   - Business rules remain consistent regardless of storage or notification mechanism

3. **Customer Relationship Management**:
   - Core domain: Customer management and interaction logic
   - Ports: CustomerRepository, CommunicationService, AnalyticsService interfaces
   - Driving adapters: Web controllers, CLI, API endpoints
   - Driven adapters: MongoDB adapter, email provider, analytics platform
   - Core functionality protected from changes in external systems

4. **Content Management System**:
   - Core domain: Content creation, workflow, and publishing logic
   - Ports: ContentRepository, SearchService, MediaService interfaces
   - Driving adapters: Admin UI, public API, webhooks
   - Driven adapters: Database storage, Elasticsearch, cloud storage
   - Content management rules isolated from storage and delivery mechanisms

### Anti-Corruption Layer

An Anti-Corruption Layer is a boundary that preserves the integrity of a domain model by translating between different models or systems, often used when integrating with legacy systems.

**Functions**:
- Translation between different domain models
- Isolation from legacy or external systems
- Protection from changes in external systems
- Gradual migration enablement

**Examples**:

1. **Legacy System Modernization**:
   - New microservices architecture with modern domain model
   - Anti-corruption layer translates between new domain concepts and legacy data model
   - Legacy system continues to operate while new system is developed incrementally
   - Translation handles data format differences and business rule variations
   - New system can evolve independently without being constrained by legacy design

2. **Enterprise Application Integration**:
   - Modern CRM system needs to integrate with legacy ERP system
   - Anti-corruption layer translates between CRM's customer model and ERP's account structure
   - Layer handles differences in terminology, data formats, and business rules
   - CRM can maintain its clean domain model without ERP contamination
   - Changes in either system require updates only to the translation layer

3. **Acquisition Integration**:
   - Company acquires another with incompatible customer management systems
   - Anti-corruption layer enables data flow between different customer models
   - Translation handles different customer IDs, address formats, and status codes
   - Both systems can continue operating during gradual migration
   - Business operations continue without disruption during integration

4. **Third-party API Integration**:
   - E-commerce platform integrates with multiple shipping providers
   - Anti-corruption layer translates between internal shipping model and provider-specific APIs
   - Layer handles different address formats, service codes, and tracking systems
   - Internal system maintains consistent domain model regardless of provider
   - New shipping providers can be added by implementing new translators

### Sharded Architecture

Sharded Architecture is a database architecture pattern where data is horizontally partitioned across multiple database instances to improve scalability and performance.

**Key characteristics**:
- Data is divided across multiple database instances based on a shard key
- Each shard contains a subset of the data and operates independently
- Shards can be distributed across different servers or data centers
- Enables horizontal scaling for large datasets and high throughput

**Examples**:

1. **Social Media Platform**:
   - User data sharded by user ID ranges (users 1-1M on shard 1, 1M-2M on shard 2, etc.)
   - Each shard contains complete user profiles, posts, and connections
   - Queries for a specific user's data go directly to the appropriate shard
   - New shards added as user base grows
   - Cross-shard operations handled by application layer when necessary

2. **E-commerce Order System**:
   - Orders sharded by customer region (North America, Europe, Asia, etc.)
   - Each regional shard contains orders for customers in that region
   - Localized database instances reduce latency for regional customers
   - Regional traffic spikes affect only the relevant shard
   - Historical orders moved to archival shards based on age

3. **IoT Data Platform**:
   - Device data sharded by device type and time period
   - Current data kept in active shards, historical data in archive shards
   - High-volume sensors get dedicated shards to handle throughput
   - Sharding strategy optimized for time-series queries
   - New shards automatically provisioned as data volume grows

4. **Multi-tenant SaaS Application**:
   - Data sharded by tenant with large tenants on dedicated shards
   - Small tenants grouped on shared shards based on geography
   - Tenant-specific customizations stored with tenant data
   - Noisy neighbor problems minimized through isolation
   - Tenant migration between shards possible for growing customers

### Event-Driven Architecture

Event-Driven Architecture is a design pattern where components communicate through events, with producers emitting events and consumers reacting to them, often asynchronously.

**Key components**:
- Event producers: Generate events when something notable happens
- Event consumers: React to events they're interested in
- Event broker/bus: Routes events from producers to consumers
- Event store: Persists events for replay or audit

**Examples**:

1. **E-commerce Order Processing**:
   - Order Service publishes "OrderCreated" event when customer places order
   - Inventory Service consumes event and reserves inventory
   - Payment Service processes payment and publishes "PaymentProcessed"
   - Shipping Service prepares shipment after receiving "PaymentProcessed"
   - Analytics Service updates dashboards with all events
   - Email Service sends confirmation to customer
   - Each service reacts independently without direct coupling

2. **Banking Transaction System**:
   - Account Service publishes "TransactionCreated" events
   - Fraud Detection Service analyzes transactions for suspicious patterns
   - Notification Service alerts customers of large transactions
   - Rewards Service calculates points for eligible purchases
   - Regulatory Reporting Service logs transactions for compliance
   - Each service performs its function without direct dependencies

3. **IoT Monitoring Platform**:
   - Devices publish telemetry events to central event bus
   - Alerting Service monitors for threshold violations
   - Visualization Service updates real-time dashboards
   - Storage Service archives events for historical analysis
   - Machine Learning Service analyzes patterns for predictive maintenance
   - New consumers can be added without modifying existing components

4. **Content Management System**:
   - Content Service publishes "ContentPublished" events
   - Search Indexing Service updates search indexes
   - CDN Cache Service invalidates cached content
   - Social Media Service posts updates to connected platforms
   - Analytics Service tracks content performance
   - Subscriber Service notifies subscribed users
   - System remains extensible as new features are added

### Space-Based Architecture

Space-Based Architecture (also known as Cloud Architecture) is designed to address high scalability and high concurrency problems by removing the central database as a bottleneck.

**Key components**:
- Processing units: Application code that processes data
- Virtualized middleware: Distributed in-memory data grid
- Data pumps: Move data between processing grid and database
- Data writers: Asynchronously update the database

**Examples**:

1. **High-frequency Trading Platform**:
   - Processing units handle trade execution in-memory
   - Virtualized middleware distributes market data across processing units
   - Data grid maintains consistent view of positions and orders
   - Data pumps asynchronously write completed trades to persistent storage
   - System can process millions of transactions per second with low latency

2. **Online Gaming Platform**:
   - Game state maintained in distributed in-memory data grid
   - Processing units handle player actions and game logic
   - Players interact with nearest processing unit for low latency
   - State changes replicated across grid for consistency
   - Persistent storage updated asynchronously for game history and stats

3. **Real-time Bidding System**:
   - Bid requests processed by in-memory processing units
   - Advertiser data distributed across virtualized middleware
   - Processing units make bid decisions in milliseconds
   - Results synchronized across data grid
   - Historical bid data asynchronously written to analytics database

4. **IoT Data Processing Platform**:
   - Sensor data streams processed by distributed processing units
   - Current state maintained in in-memory data grid
   - Processing units apply rules and analytics in real-time
   - Alerts generated based on pattern detection
   - Raw data asynchronously archived to persistent storage
   - System scales horizontally as more devices are added

### Layered Architecture

Layered Architecture organizes components into horizontal layers, where each layer has a specific role and responsibility, and typically only communicates with adjacent layers.

**Common layers**:
- Presentation layer: User interface and user experience
- Application layer: Orchestrates business processes
- Domain layer: Core business logic and rules
- Infrastructure layer: Technical capabilities and external interfaces

**Examples**:

1. **Enterprise Resource Planning (ERP) System**:
   - Presentation layer: Web interface, mobile app, and reporting dashboards
   - Application layer: Service components orchestrating business processes
   - Domain layer: Core business logic implementing company policies and workflows
   - Infrastructure layer: Data access, external system integration, and messaging
   - Each layer has clear responsibilities and dependencies flow downward

2. **Banking Application**:
   - Presentation layer: Customer portal, teller interface, and mobile banking app
   - Application layer: Transaction processing, account management services
   - Domain layer: Financial rules, interest calculations, and compliance logic
   - Infrastructure layer: Database access, payment network integration, security
   - Strict separation ensures security and regulatory controls are consistently applied

3. **Healthcare Management System**:
   - Presentation layer: Patient portal, clinical interface, administrative dashboard
   - Application layer: Appointment scheduling, billing services, care coordination
   - Domain layer: Medical protocols, treatment plans, insurance rules
   - Infrastructure layer: Electronic health record storage, lab system integration
   - Layering helps manage complexity and enforce HIPAA compliance at appropriate levels

4. **E-commerce Platform**:
   - Presentation layer: Storefront, admin portal, mobile shopping app
   - Application layer: Order processing, catalog management, customer services
   - Domain layer: Pricing rules, inventory management, promotion logic
   - Infrastructure layer: Database access, payment processing, shipping integration
   - Layers can be scaled independently based on different load characteristics

### Service-Oriented Architecture (SOA)

Service-Oriented Architecture is an architectural style where business functionality is provided as a set of services that can be used by other applications through a communication protocol over a network.

**Key principles**:
- Standardized service contracts
- Loose coupling between services
- Service abstraction
- Service reusability
- Service autonomy
- Service statelessness
- Service discoverability

**Examples**:

1. **Enterprise Integration Platform**:
   - Customer Management Service: Provides customer data and operations
   - Order Processing Service: Handles order creation and fulfillment
   - Billing Service: Manages invoices and payments
   - Inventory Service: Tracks product availability
   - Multiple applications (web portal, mobile app, kiosks) consume these services
   - Enterprise Service Bus (ESB) handles message routing and transformation

2. **Banking System**:
   - Account Service: Provides account information and transactions
   - Customer Service: Manages customer profiles and relationships
   - Loan Service: Handles loan applications and management
   - Card Service: Manages credit and debit card operations
   - Services used by branch applications, online banking, and ATM systems
   - Services implement security and compliance requirements consistently

3. **Healthcare Provider Network**:
   - Patient Service: Manages patient information and history
   - Appointment Service: Handles scheduling across facilities
   - Provider Service: Maintains doctor and specialist information
   - Insurance Service: Verifies coverage and processes claims
   - Services consumed by hospital systems, clinics, and patient portals
   - Standardized interfaces ensure consistent data across the network

4. **Telecommunications Platform**:
   - Subscriber Service: Manages customer accounts and subscriptions
   - Billing Service: Handles usage tracking and invoice generation
   - Network Service: Provides network status and configuration
   - Provisioning Service: Activates and configures services
   - Multiple channels (retail, online, call center) use these services
   - Service contracts ensure backward compatibility for consumers

### Serverless Architecture

Serverless architecture is a cloud computing execution model where the cloud provider dynamically manages the allocation and provisioning of servers. Applications run in stateless compute containers that are event-triggered and fully managed by the provider.

**Key characteristics**:
- No server management
- Pay-per-execution pricing
- Auto-scaling
- Event-driven
- Stateless functions
- Ephemeral compute environments

**Examples**:

1. **Image Processing Application**:
   - User uploads image to cloud storage (S3, Azure Blob)
   - Upload event triggers serverless function
   - Function processes image (resizing, filtering, optimization)
   - Processed image saved to storage and metadata to database
   - User notified when processing completes
   - System automatically scales during high-volume periods
   - No infrastructure management required

2. **E-commerce Order Processing**:
   - Order placement triggers serverless checkout function
   - Function validates order and processes payment
   - Success triggers inventory update function
   - Notification function sends confirmation email
   - Each function scales independently based on load
   - Pay-per-execution model reduces costs during quiet periods

3. **IoT Data Processing Pipeline**:
   - IoT devices send data to message queue
   - Messages trigger serverless processing functions
   - Functions validate, transform, and analyze data
   - Anomaly detection function triggers alerts when needed
   - Processed data stored in database for reporting
   - System handles variable data volumes without provisioning

4. **API Backend for Mobile App**:
   - Mobile app calls API Gateway endpoints
   - API Gateway triggers appropriate serverless functions
   - Authentication function validates user credentials
   - Data retrieval functions fetch from databases
   - Update functions process user submissions
   - Backend scales automatically with user base
   - Development focuses on business logic, not infrastructure

**Example technologies**:
- AWS Lambda
- Azure Functions
- Google Cloud Functions
- Knative

### Mesh Architecture

Mesh Architecture is a network topology where each node in the network can act as both a client and a server, enabling direct, dynamic, and non-hierarchical cooperation between nodes.

**Key characteristics**:
- Peer-to-peer communication
- No central coordination
- Self-organization
- Resilience through redundancy
- Dynamic discovery and routing

**Examples**:

1. **Service Mesh for Microservices**:
   - Each microservice has a proxy sidecar (e.g., Envoy)
   - Sidecars form a mesh network for service-to-service communication
   - Control plane (e.g., Istio, Linkerd) configures all proxies
   - Mesh handles service discovery, load balancing, and circuit breaking
   - Provides consistent observability across all services
   - Enables zero-trust security with mutual TLS between services

2. **Peer-to-Peer Content Delivery**:
   - Each node can both consume and serve content
   - Content distributed across multiple nodes without central servers
   - Nodes discover each other dynamically
   - Requests routed to nearest available node with content
   - System resilient to individual node failures
   - Bandwidth costs distributed across the network

3. **IoT Mesh Network**:
   - IoT devices form a mesh network for communication
   - Devices relay messages for other devices when needed
   - No single point of failure in the network
   - Devices self-organize to find optimal routes
   - Network can extend coverage beyond direct connection range
   - New devices can join the mesh automatically

4. **Distributed Database Cluster**:
   - Database nodes form a mesh for data replication
   - Each node can accept reads and writes
   - Data automatically replicated across the mesh
   - Nodes communicate directly without central coordinator
   - System remains available during partial network partitions
   - Mesh reconfigures when nodes join or leave

### Cell-Based Architecture

Cell-Based Architecture organizes components into semi-autonomous units called cells, each containing a complete stack of functionality and capable of operating independently.

**Key characteristics**:
- Self-contained units with their own compute, networking, and data
- Cells have well-defined boundaries and interfaces
- Limited blast radius for failures
- Independent scaling and deployment
- Hierarchical organization possible (cells within cells)

**Examples**:

1. **Global E-commerce Platform**:
   - North America cell: Complete stack of services for NA customers
   - Europe cell: Complete stack for European customers
   - Asia-Pacific cell: Complete stack for APAC customers
   - Each cell contains its own services, databases, and caches
   - Regional failures only affect customers in that region
   - Cross-cell communication for global inventory and analytics
   - New regions can be added by deploying new cells

2. **Multi-tenant SaaS Application**:
   - Enterprise customers get dedicated cells with isolated resources
   - SMB customers grouped into shared cells by region
   - Each cell contains all services needed for full functionality
   - Cell boundaries prevent noisy neighbor problems
   - Resource limits enforced at the cell level
   - Cells can be customized for specific customer requirements

3. **Financial Services Platform**:
   - Retail banking cell: Services for consumer accounts and transactions
   - Investment banking cell: Services for trading and portfolio management
   - Insurance cell: Services for policy management and claims
   - Each cell operates independently with its own compliance controls
   - Cross-cell services handle customer identity and reporting
   - Failures in one business line don't affect others

4. **Telecommunications Network**:
   - Regional cells based on network topology
   - Each cell contains subscriber management, billing, and service provisioning
   - Cell boundaries align with physical network boundaries
   - Traffic contained within cells when possible
   - Inter-cell gateways handle roaming and cross-region services
   - Maintenance and upgrades can be performed cell by cell

## Best Practices

1. **Choose patterns based on requirements**: Select architectural patterns that align with your specific business and technical requirements.

2. **Combine patterns when appropriate**: Many systems use multiple patterns together to address different concerns.

3. **Consider trade-offs**: Every pattern has advantages and disadvantages. Understand the trade-offs before implementation.

4. **Start simple**: Begin with simpler patterns and evolve as needed rather than over-engineering from the start.

5. **Document architectural decisions**: Use Architecture Decision Records (ADRs) to document why specific patterns were chosen.

6. **Validate with prototypes**: Test critical aspects of the chosen architecture with prototypes before full implementation.

7. **Plan for evolution**: Design your architecture to evolve over time as requirements change.

## Pitfalls

1. **Pattern overuse**: Applying complex patterns when simpler solutions would suffice.

2. **Ignoring organizational context**: Choosing patterns that don't align with team skills or organizational structure.

3. **Premature optimization**: Implementing complex distributed patterns before they're needed.

4. **Neglecting operational concerns**: Choosing architectures that are difficult to deploy, monitor, or troubleshoot.

5. **Rigid implementation**: Implementing patterns too rigidly without adapting to specific needs.

6. **Missing security implications**: Failing to consider security aspects of chosen patterns.

7. **Overlooking data consistency**: Not addressing data consistency challenges in distributed patterns.

## Learning Outcomes

After studying these architectural patterns, you should be able to:

1. Identify appropriate architectural patterns for different types of systems and requirements
2. Understand the trade-offs between different architectural approaches
3. Apply patterns in combination to address complex architectural challenges
4. Recognize when to evolve from one pattern to another as system requirements change
5. Communicate architectural decisions using standard terminology and concepts
6. Evaluate existing systems through the lens of established architectural patterns
7. Design systems that balance immediate needs with future flexibility
