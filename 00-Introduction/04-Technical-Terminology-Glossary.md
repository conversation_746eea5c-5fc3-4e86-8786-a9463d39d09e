# Technical Terminology Glossary

**Skill Level: Beginner to Advanced**

## Notes

This comprehensive glossary provides definitions and explanations for technical terms, patterns, and concepts used in modern DevOps, cloud-native, and software architecture practices. It includes terminology referenced throughout the DevOps Architect study plan as well as additional important concepts from the current industry landscape. Understanding these terms is essential for effective communication and implementation of DevOps practices and architectural patterns.


## Development Practices

### Trunk-Based Development

Trunk-Based Development is a source control branching model where developers collaborate on code in a single branch called "trunk" (or "main" or "master"). Features are developed using short-lived feature branches or directly in the trunk using feature toggles.

**Key characteristics**:
- Short-lived branches (typically less than a day)
- Frequent merges to the trunk (at least daily)
- Continuous integration on the trunk
- Feature toggles to hide incomplete work
- No long-running feature branches

**Contrast with GitFlow**: Unlike GitFlow, which uses multiple long-running branches (develop, release, feature, hotfix), Trunk-Based Development emphasizes a single main branch with short-lived feature branches.

### Feature Toggles (Feature Flags)

Feature toggles are a technique that allows teams to modify system behavior without changing code. They enable:

1. **Deployment separate from release**: Code can be deployed to production but not activated
2. **A/B testing**: Different users see different features
3. **Canary releases**: Gradually rolling out features to subsets of users
4. **Kill switches**: Quickly disabling problematic features

**Types of feature toggles**:
- **Release toggles**: Hide incomplete features in production
- **Experiment toggles**: For A/B testing
- **Operational toggles**: Control operational aspects like performance features
- **Permission toggles**: Enable features for specific user groups

**Example**: A team might deploy a new search algorithm behind a feature toggle, initially enabling it for internal users only, then for 5% of customers, and gradually increasing to 100% if metrics are positive.

### Blue-Green Deployment

Blue-Green Deployment is a technique for releasing applications by shifting traffic between two identical environments:

1. **Blue environment**: The currently running production environment
2. **Green environment**: The new version of the application

The process involves:
1. Deploy the new version to the green environment
2. Test the green environment
3. Switch traffic from blue to green (often using a load balancer)
4. Keep the blue environment as a fallback for quick rollback

**Benefits**:
- Zero downtime deployments
- Immediate rollback capability
- Complete testing in a production-like environment

### Canary Deployment

Canary Deployment is a technique for reducing risk when releasing new software versions by gradually rolling out changes to a small subset of users before making them available to everyone.

The process typically involves:
1. Deploy the new version to a small portion of the infrastructure
2. Route a small percentage of users to the new version
3. Monitor for any issues or degradation
4. Gradually increase the percentage if no issues are found
5. Complete the rollout when confident in the new version

**Example**: A web service might first deploy a new version to 5% of its servers and route 5% of traffic to them, monitoring error rates and performance before increasing to 20%, then 50%, and finally 100%.

## DevOps Concepts

### GitOps

GitOps is an operational framework that applies DevOps best practices for infrastructure automation using Git as the single source of truth. Key principles include:

1. The entire system is described declaratively
2. The canonical desired system state is versioned in Git
3. Approved changes to the desired state are automatically applied
4. Software agents ensure correctness and alert on divergence

**Example**: In a Kubernetes environment with GitOps:
- Infrastructure and application configurations are stored in Git repositories
- A tool like Flux or ArgoCD continuously monitors these repositories
- When changes are committed and merged, the tool automatically applies them to the cluster
- The tool also detects and reports any drift between the desired state and actual state

### Infrastructure as Code (IaC)

Infrastructure as Code is the practice of managing and provisioning infrastructure through machine-readable definition files rather than manual processes. Key aspects include:

1. Infrastructure defined using code and declarative definitions
2. The same version control practices applied to infrastructure as application code
3. Automated testing and validation of infrastructure changes
4. Consistent and repeatable infrastructure deployments

**Common IaC tools**:
- Terraform
- AWS CloudFormation
- Azure Resource Manager templates
- Google Cloud Deployment Manager
- Pulumi
- Ansible (for configuration management)

### Continuous Integration (CI)

Continuous Integration is the practice of frequently integrating code changes into a shared repository, followed by automated building and testing. Key components include:

1. Developers merge changes to the main branch frequently
2. Each merge triggers an automated build and test process
3. Fast feedback on the quality and correctness of changes
4. Issues are detected and fixed quickly

**Example CI workflow**:
1. Developer commits code to a feature branch
2. Developer creates a pull request to merge to main
3. CI system automatically builds the code and runs tests
4. If tests pass, the code can be merged
5. After merging, CI runs again on the main branch

### Continuous Delivery (CD)

Continuous Delivery extends Continuous Integration by automatically deploying all code changes to a testing or staging environment after the build stage. Key aspects include:

1. Automated deployment to non-production environments
2. Manual approval for production deployment
3. The ability to deploy any successful build to production
4. Deployment pipeline with multiple stages (build, test, staging, production)

### Continuous Deployment

Continuous Deployment goes one step further than Continuous Delivery by automatically deploying every change that passes all stages of the production pipeline to production. There is no manual approval step between commit and production.

**Difference from Continuous Delivery**: Continuous Delivery ensures code is always in a deployable state but requires manual approval for production deployment. Continuous Deployment automatically deploys every change that passes tests to production without human intervention.

## Cloud and Container Concepts

### Service Mesh

Service mesh has been moved to the [Architectural Patterns](05-Architectural-Patterns.md) file.

### Container Orchestration

Container orchestration automates the deployment, scaling, networking, and management of containerized applications. Key capabilities include:

1. Scheduling containers across a cluster of machines
2. Scaling containers up or down based on demand
3. Load balancing traffic between container instances
4. Managing storage and networking
5. Self-healing by replacing failed containers
6. Rolling updates and rollbacks

**Popular container orchestration platforms**:
- Kubernetes
- Amazon ECS
- Azure Container Instances
- Docker Swarm
- Google Kubernetes Engine (GKE)

### Immutable Infrastructure

Immutable Infrastructure is an approach where servers and deployment artifacts are never modified after they're deployed. If changes are needed, new servers are built and deployed, and the old ones are decommissioned.

**Benefits**:
- Consistency and reliability
- Simplified rollbacks
- Reduced configuration drift
- Improved security

**Example**: Instead of updating packages on existing servers, a new server image with updated packages is created, tested, and deployed. Old servers are then removed once the new ones are confirmed working.

## Observability Concepts

### The Three Pillars of Observability

Observability refers to the ability to understand the internal state of a system based on its external outputs. The three pillars are:

1. **Metrics**: Numerical data about system performance and behavior
   - Examples: CPU usage, request count, error rate, response time

2. **Logs**: Timestamped records of discrete events
   - Examples: Application logs, system logs, audit logs

3. **Traces**: Records of requests as they flow through distributed systems
   - Examples: Request path through microservices, timing for each service

### SLI, SLO, and SLA

These terms define how reliability is measured and guaranteed:

1. **Service Level Indicator (SLI)**: A quantitative measure of service level
   - Examples: Availability percentage, response time, error rate

2. **Service Level Objective (SLO)**: A target value or range for an SLI
   - Example: "99.9% availability over a 30-day period"

3. **Service Level Agreement (SLA)**: A contract that includes consequences of meeting or missing SLOs
   - Example: "99.9% availability or customer receives service credits"

### Error Budget

An error budget is the amount of unreliability that's "allowed" for a service, derived from its SLO. For example:

- If a service has a 99.9% availability SLO
- Its error budget is 0.1% (100% - 99.9%)
- This translates to about 43.8 minutes of downtime per month

Error budgets help teams balance reliability work against feature development.

## Architecture Patterns

Architectural patterns have been moved to a dedicated file: [05-Architectural-Patterns.md](05-Architectural-Patterns.md)

This separate file contains comprehensive information about various architectural patterns including:
- Microservices Architecture
- API Gateway Pattern
- Backend for Frontend (BFF) Pattern
- Bulkhead Pattern
- Domain-Driven Design (DDD)
- Hexagonal Architecture
- Anti-Corruption Layer
- Sharded Architecture
- Event-Driven Architecture
- Space-Based Architecture
- Layered Architecture
- Service-Oriented Architecture (SOA)
- And many more

Please refer to the dedicated architectural patterns file for detailed information about these patterns.

## Cloud-Native Concepts

### Function as a Service (FaaS)

Function as a Service is a category of cloud computing services that provides a platform for developing, running, and managing application functionalities without the complexity of building and maintaining infrastructure.

**Key features**:
- Single-purpose, stateless functions
- Event-triggered execution
- Automatic scaling
- Usage-based billing
- No server management

**Example**: A image processing service might use FaaS to resize uploaded images, with functions that are triggered by new uploads and scale automatically based on demand.

### Service Discovery

Service Discovery is the process of automatically detecting devices and services on a network. In microservices architectures, it enables services to find and communicate with each other without hardcoded locations.

**Implementation approaches**:
- Client-side discovery: Clients query a service registry
- Server-side discovery: Clients go through a router/load balancer
- DNS-based discovery: Using DNS for service lookup
- Mesh-based discovery: Service mesh handles discovery

**Example technologies**:
- Consul
- etcd
- Kubernetes Service
- Eureka
- ZooKeeper

### Zero Trust Security Model

The Zero Trust security model operates on the principle "never trust, always verify," requiring strict identity verification for every person and device trying to access resources, regardless of whether they are inside or outside the network perimeter.

**Key principles**:
- Verify explicitly: Always authenticate and authorize
- Use least privilege access: Limit user access rights
- Assume breach: Minimize blast radius and segment access

**Example implementation**:
- Multi-factor authentication for all users
- Micro-segmentation of networks
- Just-in-time and just-enough access
- Continuous monitoring and validation

### Cloud-Native Storage

Cloud-Native Storage refers to storage solutions designed specifically for containerized and cloud environments, providing dynamic provisioning, scalability, and integration with container orchestration platforms.

**Key features**:
- Container Storage Interface (CSI) support
- Dynamic provisioning
- Storage classes and policies
- Persistent volumes
- Data protection and replication
- Multi-cloud support

**Example technologies**:
- Rook
- Portworx
- Longhorn
- OpenEBS
- AWS EBS CSI driver

### Service Mesh

Service mesh has been moved to the [Architectural Patterns](05-Architectural-Patterns.md) file.

## DevOps Practices and Methodologies

### Value Stream Mapping

Value Stream Mapping is a lean technique used to analyze, design, and manage the flow of materials and information required to bring a product or service to a customer, applied to software delivery pipelines.

**Key components**:
- Process steps and their sequence
- Time measurements (processing time, wait time)
- Information flows
- Inventory or queues between steps
- Identification of waste and bottlenecks

**Example**: A DevOps team might create a value stream map of their software delivery process, identifying that code reviews create a significant bottleneck, leading to process improvements.

### Shift-Left Testing

Shift-Left Testing is the practice of moving testing earlier in the software development lifecycle, finding and fixing issues sooner when they are less expensive to address.

**Implementation approaches**:
- Test-Driven Development (TDD)
- Behavior-Driven Development (BDD)
- Automated unit and integration testing
- Static code analysis
- Early security testing

**Example**: Instead of waiting for a dedicated QA phase, developers write automated tests before implementing features and run security scans as part of their local development process.

### ChatOps

ChatOps is a collaboration model that connects people, tools, processes, and automation into a transparent workflow using chat platforms as the primary interface.

**Key components**:
- Chat platform (Slack, Microsoft Teams)
- Chatbots and integrations
- Command-line interfaces within chat
- Automation scripts and workflows
- Notifications and alerts

**Example**: A team might use a Slack channel with integrated bots to deploy applications, check system status, and receive alerts, making operations visible to the entire team.

### DevSecOps

DevSecOps integrates security practices within the DevOps process, making security a shared responsibility throughout the entire IT lifecycle.

**Key practices**:
- Security as code
- Automated security testing
- Vulnerability scanning
- Compliance as code
- Security monitoring and response
- Threat modeling

**Example**: A DevSecOps pipeline might include automated SAST/DAST scanning, container image scanning, and compliance checks that run automatically with each code commit.

### FinOps

FinOps (Cloud Financial Operations) is a cultural practice focused on bringing financial accountability to the variable spend model of cloud, enabling distributed teams to make business trade-offs between speed, cost, and quality.

**Core principles**:
- Teams collaborate on cloud cost optimization
- Everyone takes ownership of their cloud usage
- Reports should be accessible and timely
- Decisions are driven by business value of cloud
- Take advantage of the variable cost model of cloud

**Example practices**:
- Tagging resources for cost allocation
- Rightsizing instances
- Implementing auto-scaling
- Using spot/preemptible instances
- Implementing cost anomaly detection

### Site Reliability Engineering (SRE)

Site Reliability Engineering is a discipline that incorporates aspects of software engineering and applies them to infrastructure and operations problems, with a focus on creating scalable and highly reliable software systems.

**Key concepts**:
- Service Level Objectives (SLOs)
- Error budgets
- Toil reduction
- Automation
- Monitoring and observability
- Blameless postmortems

**Example**: An SRE team might define SLOs for their services, use error budgets to balance reliability work against feature development, and automate repetitive operational tasks.

## Deployment and Release Strategies

### Dark Launching

Dark Launching is the practice of releasing features to a subset of users or environments without making them visible or accessible to everyone, allowing testing in production-like conditions.

**Implementation approaches**:
- Feature flags with limited user targeting
- Shadow testing with production traffic
- Limited geographical rollout
- Employee-only access

**Example**: A social media platform might dark launch a new news feed algorithm to 1% of users to evaluate performance and engagement metrics before wider release.

### Shadow Testing

Shadow Testing involves processing real production traffic in parallel through both the current production system and a new system, comparing the results without affecting users.

**Key characteristics**:
- Duplicate traffic sent to new system
- Results compared but not returned to users
- Real production patterns and volume
- Zero impact on user experience

**Example**: Before replacing a recommendation engine, a company might shadow test the new algorithm by processing the same requests as the current system and comparing the recommendations generated.

### Progressive Delivery

Progressive Delivery extends continuous delivery by gradually rolling out new features to users, monitoring their impact, and responding accordingly.

**Key techniques**:
- Feature flags
- Canary releases
- A/B testing
- Blue/green deployments
- Traffic shifting

**Example**: A team might release a new feature to 5% of users, monitor key metrics, increase to 20% if successful, and continue gradually until reaching 100%.

### A/B Testing

A/B Testing is a method of comparing two versions of a webpage, feature, or application against each other to determine which one performs better according to defined metrics.

**Key components**:
- Control version (A)
- Variant version(s) (B, C, etc.)
- Random user assignment
- Statistical analysis
- Defined success metrics

**Example**: An e-commerce site might test two different checkout flows to see which one results in higher conversion rates, showing version A to 50% of users and version B to the other 50%.

### Traffic Shifting

Traffic Shifting is the practice of gradually redirecting user traffic from one version of an application to another, often used in canary deployments and blue/green deployments.

**Implementation approaches**:
- Load balancer configuration
- Service mesh routing rules
- DNS-based routing
- API gateway routing

**Example**: When deploying a new version, traffic might be shifted in increments (5%, 20%, 50%, 100%) while monitoring for any issues at each stage.

## Container and Kubernetes Concepts

### Pod Security Policies

Pod Security Policies are cluster-level resources in Kubernetes that control security-sensitive aspects of pod specification, defining a set of conditions that pods must meet to be accepted into the system.

**Key controls**:
- Privileged container usage
- Host namespaces usage
- Host network and ports
- Volume types
- User and group IDs
- Linux capabilities
- SELinux context

**Note**: In Kubernetes v1.25+, Pod Security Policies were replaced by Pod Security Admission, which uses the Pod Security Standards.

### Custom Resource Definitions (CRDs)

Custom Resource Definitions extend the Kubernetes API by defining custom resources that can be managed using kubectl and other Kubernetes clients.

**Key components**:
- Schema definition
- Validation rules
- Versioning
- Scope (namespaced or cluster-wide)
- Custom controllers (optional)

**Example**: A database operator might define CRDs for databases, users, and backups, allowing these resources to be managed through Kubernetes manifests.

### Kubernetes Operators

Kubernetes Operators are software extensions to Kubernetes that make use of custom resources to manage applications and their components, automating complex, application-specific operational knowledge.

**Key capabilities**:
- Application installation and configuration
- Upgrades and patches
- Failover and recovery
- Scaling
- Backup and restore
- Custom health checks

**Example**: A PostgreSQL operator might automate tasks like provisioning databases, managing replication, handling failovers, and performing backups.

### Helm

Helm is a package manager for Kubernetes that helps define, install, and upgrade complex Kubernetes applications using charts.

**Key concepts**:
- Charts: Packages of pre-configured Kubernetes resources
- Repositories: Collections of charts
- Releases: Instances of charts running in a cluster
- Values: Configuration for chart customization
- Templates: Parameterized Kubernetes manifests

**Example**: A team might use a Helm chart to deploy a complex application stack including web servers, databases, and caching layers, with different values files for development, staging, and production environments.

### Kustomize

Kustomize is a Kubernetes configuration management tool that allows customization of YAML manifests without templates, using a declarative approach.

**Key features**:
- Base and overlay structure
- Patches for modifying resources
- ConfigMap and Secret generation
- Cross-cutting fields modification
- Resource composition

**Example**: A team might have a base Kubernetes configuration with overlays for different environments that add environment-specific configurations and resources.

### Container Runtime Interface (CRI)

The Container Runtime Interface is a plugin interface that enables the Kubernetes kubelet to use different container runtimes without recompiling.

**Common CRI implementations**:
- containerd
- CRI-O
- Docker (via cri-dockerd)
- Kata Containers

**Example**: A Kubernetes cluster might use containerd as its container runtime, which implements the CRI to interact with the kubelet.



## Data Management and Persistence

### Change Data Capture (CDC)

Change Data Capture is a set of software design patterns used to determine and track changes in data so that action can be taken using the changed data.

**Key approaches**:
- Log-based CDC: Reading database transaction logs
- Trigger-based CDC: Using database triggers
- Query-based CDC: Polling for changes
- Event sourcing: Capturing all changes as events

**Example technologies**:
- Debezium
- AWS Database Migration Service
- Oracle GoldenGate
- SQL Server Change Data Capture

### Data Mesh

Data Mesh is a sociotechnical approach to data architecture that decentralizes data ownership to domain teams while providing centralized infrastructure as a platform.

**Key principles**:
- Domain-oriented ownership
- Data as a product
- Self-serve data infrastructure
- Federated computational governance

**Example**: Instead of a centralized data lake, each business domain (sales, marketing, operations) owns and serves its data as products, with standardized interfaces and quality guarantees.

### Database Sharding

Database Sharding is a horizontal partitioning technique that splits a database into smaller, more manageable pieces called shards, distributed across multiple servers.

**Sharding strategies**:
- Range-based: Partitioning by value ranges
- Hash-based: Using a hash function to determine shard
- Directory-based: Using a lookup service
- Geography-based: Partitioning by location

**Example**: A social media platform might shard user data across multiple database servers based on user ID ranges, with users 1-1M on one shard, 1M-2M on another, and so on.

### Write-Ahead Logging (WAL)

Write-Ahead Logging is a technique for providing atomicity and durability in database systems by recording changes in a log before applying them to the database.

**Key characteristics**:
- Changes are first recorded in the log
- Log is flushed to persistent storage
- Only then are changes applied to the database
- Enables recovery after crashes

**Example technologies**:
- PostgreSQL WAL
- MySQL binary log
- SQLite journal
- Kafka transaction log

### Eventual Consistency

Eventual Consistency is a consistency model used in distributed systems that guarantees that, if no new updates are made to a given data item, eventually all accesses to that item will return the last updated value.

**Key characteristics**:
- System will become consistent over time
- Temporary inconsistencies are allowed
- Higher availability during network partitions
- Often used in distributed databases and caches

**Example**: In a distributed e-commerce system, when a customer places an order, it might be immediately visible in the orders service but take a few seconds to appear in the analytics service.

### Polyglot Persistence

Polyglot persistence refers to using different data storage technologies for different types of data or use cases within a single application or system. Rather than forcing all data into a single database type, this approach selects the most appropriate database technology for each specific data requirement.

**Types of databases commonly used in polyglot persistence**:
- Relational databases (PostgreSQL, MySQL) for structured data with complex relationships
- Document databases (MongoDB, Couchbase) for semi-structured data
- Key-value stores (Redis, DynamoDB) for simple, high-throughput data access
- Graph databases (Neo4j) for highly connected data
- Time-series databases (InfluxDB, Prometheus) for metrics and monitoring data
- Search engines (Elasticsearch) for full-text search capabilities

**Example**: A social media application might use:
- PostgreSQL for user profiles and relationships
- Redis for session management and caching
- MongoDB for storing user-generated content
- Elasticsearch for search functionality
- Neo4j for analyzing connection networks

## Observability and Monitoring

### Chaos Engineering

Chaos Engineering is the discipline of experimenting on a system to build confidence in its capability to withstand turbulent conditions in production.

**Key principles**:
- Start with a baseline (steady state)
- Hypothesize about outcome
- Introduce real-world events (server failure, traffic spikes)
- Minimize blast radius
- Run experiments in production

**Example tools**:
- Chaos Monkey
- Gremlin
- Litmus
- Chaos Mesh

### Distributed Tracing

Distributed Tracing is a method used to profile and monitor applications, especially those built using a microservices architecture, by tracking a request as it flows through the various services.

**Key components**:
- Trace: End-to-end request flow
- Span: Individual operation within a trace
- Context propagation: Passing trace information between services
- Sampling: Selecting which traces to collect
- Visualization: Displaying trace data

**Example technologies**:
- Jaeger
- Zipkin
- OpenTelemetry
- AWS X-Ray
- Datadog APM

### Synthetic Monitoring

Synthetic Monitoring involves creating artificial users to simulate user pathways through critical business functions at regular intervals, checking availability, performance, and functionality.

**Types of synthetic monitoring**:
- API checks
- Browser-based user journeys
- Transaction monitoring
- Global performance monitoring
- Availability checks

**Example**: An e-commerce site might run synthetic tests every 5 minutes that simulate a user logging in, searching for a product, adding it to cart, and checking out, alerting if any step fails or takes too long.

### Golden Signals

Golden Signals are four key metrics that provide a comprehensive view of service health and performance, popularized by Google's Site Reliability Engineering book.

**The four golden signals**:
- Latency: Time taken to serve a request
- Traffic: Demand on the system
- Errors: Rate of failed requests
- Saturation: How "full" the service is

**Example**: A monitoring dashboard for a web service might prominently display these four metrics, with alerts configured for thresholds on each.

### Cardinality

In observability, cardinality refers to the number of unique values for a specific metric or dimension, which can significantly impact storage requirements and query performance.

**High cardinality examples**:
- User IDs
- Session IDs
- Request IDs
- IP addresses
- URLs with unique parameters

**Impact**:
- Higher storage requirements
- Slower query performance
- Increased costs
- Potential for cardinality explosion

**Example**: A metric that tracks API response times by endpoint has low cardinality (dozens of endpoints), but adding user ID as a dimension creates high cardinality (millions of unique values).

### RED Method

The RED Method is a monitoring methodology focusing on three key metrics for every service: Rate, Errors, and Duration.

**Components**:
- Rate: Requests per second
- Errors: Number of failed requests
- Duration: Distribution of response times

**Example**: A service dashboard might show the request rate over time, error percentage, and p50/p95/p99 latency, giving a quick overview of service health.

### USE Method

The USE Method is a methodology for analyzing system performance focusing on three metrics for all resources: Utilization, Saturation, and Errors.

**Components**:
- Utilization: Percentage of time the resource is busy
- Saturation: Degree to which the resource has extra work queued
- Errors: Count of error events

**Example**: Server monitoring might track CPU utilization percentage, load average (saturation), and hardware errors for each system resource.
## Additional DevOps Concepts

### GitOps

GitOps is an operational framework that applies DevOps best practices for infrastructure automation using Git as the single source of truth. Key principles include:

1. The entire system is described declaratively
2. The canonical desired system state is versioned in Git
3. Approved changes to the desired state are automatically applied
4. Software agents ensure correctness and alert on divergence

**Example**: In a Kubernetes environment with GitOps:
- Infrastructure and application configurations are stored in Git repositories
- A tool like Flux or ArgoCD continuously monitors these repositories
- When changes are committed and merged, the tool automatically applies them to the cluster
- The tool also detects and reports any drift between the desired state and actual state

### Infrastructure as Code (IaC)

Infrastructure as Code is the practice of managing and provisioning infrastructure through machine-readable definition files rather than manual processes. Key aspects include:

1. Infrastructure defined using code and declarative definitions
2. The same version control practices applied to infrastructure as application code
3. Automated testing and validation of infrastructure changes
4. Consistent and repeatable infrastructure deployments

**Common IaC tools**:
- Terraform
- AWS CloudFormation
- Azure Resource Manager templates
- Google Cloud Deployment Manager
- Pulumi
- Ansible (for configuration management)

### Continuous Integration (CI)

Continuous Integration is the practice of frequently integrating code changes into a shared repository, followed by automated building and testing. Key components include:

1. Developers merge changes to the main branch frequently
2. Each merge triggers an automated build and test process
3. Fast feedback on the quality and correctness of changes
4. Issues are detected and fixed quickly

**Example CI workflow**:
1. Developer commits code to a feature branch
2. Developer creates a pull request to merge to main
3. CI system automatically builds the code and runs tests
4. If tests pass, the code can be merged
5. After merging, CI runs again on the main branch

### Continuous Delivery (CD)

Continuous Delivery extends Continuous Integration by automatically deploying all code changes to a testing or staging environment after the build stage. Key aspects include:

1. Automated deployment to non-production environments
2. Manual approval for production deployment
3. The ability to deploy any successful build to production
4. Deployment pipeline with multiple stages (build, test, staging, production)

### Continuous Deployment

Continuous Deployment goes one step further than Continuous Delivery by automatically deploying every change that passes all stages of the production pipeline to production. There is no manual approval step between commit and production.

**Difference from Continuous Delivery**: Continuous Delivery ensures code is always in a deployable state but requires manual approval for production deployment. Continuous Deployment automatically deploys every change that passes tests to production without human intervention.

## Learning Outcomes

After studying this glossary, you should be able to:

1. Understand and explain common architectural patterns used in modern distributed systems
2. Differentiate between various deployment strategies and their use cases
3. Comprehend key DevOps practices and methodologies
4. Recognize different database types and their appropriate use cases
5. Understand observability concepts and reliability measurements
6. Communicate effectively using industry-standard terminology
7. Identify appropriate patterns for specific architectural challenges
8. Relate technical terms to practical implementation scenarios
9. Evaluate which patterns and practices are appropriate for different contexts
10. Stay current with evolving industry terminology and concepts
