# Sharded Architecture Case Study

## Overview

Sharded Architecture horizontally partitions data across multiple database instances to improve scalability and performance. This case study examines implementation at a social media platform handling millions of users.

## Business Context

**Company**: SocialConnect - A social media platform
**Challenge**: Single database becoming bottleneck with 10M+ users and growing
**Goal**: Scale database layer to handle 100M+ users with consistent performance

## Sharding Strategy

### Sharding Key Selection
- **Primary Key**: User ID (ensures even distribution)
- **Secondary Sharding**: Geographic location for some queries
- **Shard Count**: Started with 16 shards, planned for 64

### Implementation
```java
@Component
public class UserShardingStrategy {

    private static final int TOTAL_SHARDS = 16;
    private final List<DataSource> shardDataSources;

    public DataSource getShardForUser(Long userId) {
        int shardIndex = Math.abs(userId.hashCode()) % TOTAL_SHARDS;
        return shardDataSources.get(shardIndex);
    }

    public List<DataSource> getAllShards() {
        return new ArrayList<>(shardDataSources);
    }

    public DataSource getShardForNewUser() {
        // Use round-robin for new user creation to ensure even distribution
        return shardDataSources.get(ThreadLocalRandom.current().nextInt(TOTAL_SHARDS));
    }
}

@Repository
public class ShardedUserRepository {

    private final UserShardingStrategy shardingStrategy;

    public User findById(Long userId) {
        DataSource shard = shardingStrategy.getShardForUser(userId);
        JdbcTemplate jdbcTemplate = new JdbcTemplate(shard);

        return jdbcTemplate.queryForObject(
            "SELECT * FROM users WHERE id = ?",
            new Object[]{userId},
            new UserRowMapper()
        );
    }

    public void save(User user) {
        DataSource shard = shardingStrategy.getShardForUser(user.getId());
        JdbcTemplate jdbcTemplate = new JdbcTemplate(shard);

        jdbcTemplate.update(
            "INSERT INTO users (id, username, email, created_at) VALUES (?, ?, ?, ?)",
            user.getId(), user.getUsername(), user.getEmail(), user.getCreatedAt()
        );
    }

    public List<User> findUsersByPattern(String pattern) {
        // Cross-shard query - query all shards and merge results
        List<CompletableFuture<List<User>>> futures = shardingStrategy.getAllShards()
            .stream()
            .map(shard -> CompletableFuture.supplyAsync(() -> {
                JdbcTemplate jdbcTemplate = new JdbcTemplate(shard);
                return jdbcTemplate.query(
                    "SELECT * FROM users WHERE username LIKE ? LIMIT 100",
                    new Object[]{"%" + pattern + "%"},
                    new UserRowMapper()
                );
            }))
            .collect(Collectors.toList());

        return futures.stream()
            .map(CompletableFuture::join)
            .flatMap(List::stream)
            .collect(Collectors.toList());
    }
}
```

## Results and Benefits

### Performance Improvements
- **Query Performance**: 80% improvement in average query time
- **Throughput**: Increased from 10K to 100K+ queries per second
- **Scalability**: Linear scaling with additional shards
- **Availability**: Partial failures don't affect entire system

### Operational Benefits
- **Independent Scaling**: Scale individual shards based on load
- **Maintenance**: Rolling maintenance without full system downtime
- **Geographic Distribution**: Shards can be placed closer to users
- **Cost Optimization**: Right-size resources per shard

## Challenges and Solutions

### Cross-Shard Queries
**Problem**: Queries spanning multiple shards are complex and slow.
**Solution**:
- Denormalize frequently queried data
- Use application-level joins
- Implement caching for cross-shard results
- Consider read replicas for analytics

### Data Rebalancing
**Problem**: Adding new shards requires data redistribution.
**Solution**:
- Implemented consistent hashing for smoother rebalancing
- Created automated migration tools
- Used double-writing during transitions
- Planned shard splits in advance

## Best Practices Identified

1. **Choose Shard Key Carefully**: Even distribution is crucial
2. **Plan for Growth**: Design for future shard additions
3. **Minimize Cross-Shard Operations**: Denormalize when necessary
4. **Monitor Shard Health**: Track performance per shard
5. **Automate Operations**: Shard management should be automated
6. **Test Failure Scenarios**: Ensure graceful degradation

## Conclusion

Sharded Architecture enabled SocialConnect to scale from 10M to 100M+ users while maintaining performance. The key was careful shard key selection and planning for cross-shard operations from the beginning.