# Saga Pattern: Distributed Travel Booking System

## Problem Statement

**TravelNow**, a travel booking platform, is modernizing their monolithic booking system into a microservices architecture. They face several challenges:

1. Booking a trip involves multiple services (flights, hotels, car rentals, payments)
2. All parts of a booking must succeed or the entire booking should be canceled
3. Traditional distributed transactions with two-phase commit don't scale well
4. Services are owned by different teams and use different technologies
5. The system needs to be resilient to service failures
6. Customers need visibility into the booking process
7. Partial failures must be properly compensated

The team needs a solution to manage these distributed transactions while maintaining data consistency across services.

## Solution: Saga Pattern with AWS and Kubernetes

The team implements the Saga Pattern, which manages a sequence of local transactions where each transaction updates data within a single service. If a step fails, the saga executes compensating transactions to undo the changes made by the preceding steps.

### Architecture Overview

![Saga Pattern Architecture](../images/saga-pattern.png)

### Key Components

1. **Microservices**
   - **Flight Service**: Manages flight bookings (Amazon EKS)
   - **Hotel Service**: Manages hotel reservations (Amazon EKS)
   - **Car Rental Service**: Manages car rentals (Amazon EKS)
   - **Payment Service**: Processes payments (AWS Lambda)
   - **Customer Service**: Manages customer profiles (Amazon EKS)
   - **Notification Service**: Sends updates to customers (AWS Lambda)

2. **Saga Orchestration**
   - **Booking Orchestrator**: Coordinates the entire booking process (Amazon EKS)
   - **AWS Step Functions**: Manages saga workflow and state transitions
   - **Amazon EventBridge**: Routes events between services

3. **Data Storage**
   - **Amazon Aurora**: For transactional data in each service
   - **Amazon DynamoDB**: For saga state and execution history
   - **Amazon ElastiCache**: For distributed locking and caching

4. **Infrastructure**
   - **Amazon EKS**: For container orchestration
   - **AWS Lambda**: For serverless functions
   - **Amazon CloudWatch**: For monitoring and logging
   - **AWS X-Ray**: For distributed tracing

### Implementation Strategy

#### Phase 1: Service Implementation

1. **Microservice Development**
   - Implement each service with its own database
   - Define clear service boundaries and APIs
   - Ensure each service can perform local transactions
   - Implement compensating transactions for rollback

2. **API Design**
   - Design RESTful APIs for synchronous operations
   - Define event schemas for asynchronous communication
   - Implement idempotent operations for reliability
   - Create API documentation and contracts

#### Phase 2: Saga Orchestration

1. **Orchestrator Implementation**
   - Develop the Booking Orchestrator service
   - Define the saga workflow with all steps and compensations
   - Implement state management and persistence
   - Create monitoring and alerting for saga execution

2. **Step Functions Workflow**
   - Design AWS Step Functions state machine for the booking process
   - Implement error handling and retry mechanisms
   - Configure timeouts and failure paths
   - Set up execution history and logging

#### Phase 3: Compensation Handling

1. **Compensation Logic**
   - Implement compensating transactions for each service
   - Ensure idempotency for all compensating actions
   - Test failure scenarios and recovery
   - Document compensation strategies

2. **Failure Management**
   - Implement circuit breakers for service calls
   - Create dead-letter queues for failed events
   - Develop manual intervention procedures
   - Set up alerting for failed compensations

#### Phase 4: Monitoring and Observability

1. **Tracing Implementation**
   - Configure AWS X-Ray for distributed tracing
   - Implement correlation IDs across all services
   - Create custom trace annotations for business events
   - Set up dashboards for saga execution visualization

2. **Metrics and Logging**
   - Define key metrics for saga execution
   - Implement structured logging across services
   - Create CloudWatch dashboards for monitoring
   - Set up alerts for anomalies and failures

## Implementation Details

### AWS Step Functions Saga Definition

```json
{
  "Comment": "Travel Booking Saga",
  "StartAt": "ReserveFlights",
  "States": {
    "ReserveFlights": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-west-2:123456789012:function:ReserveFlights",
        "Payload": {
          "bookingId.$": "$.bookingId",
          "flightDetails.$": "$.flightDetails",
          "customerId.$": "$.customerId"
        }
      },
      "ResultPath": "$.flightReservation",
      "Catch": [
        {
          "ErrorEquals": ["States.ALL"],
          "ResultPath": "$.error",
          "Next": "BookingFailed"
        }
      ],
      "Next": "ReserveHotel"
    },
    "ReserveHotel": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-west-2:123456789012:function:ReserveHotel",
        "Payload": {
          "bookingId.$": "$.bookingId",
          "hotelDetails.$": "$.hotelDetails",
          "customerId.$": "$.customerId"
        }
      },
      "ResultPath": "$.hotelReservation",
      "Catch": [
        {
          "ErrorEquals": ["States.ALL"],
          "ResultPath": "$.error",
          "Next": "CancelFlightReservation"
        }
      ],
      "Next": "ReserveCarRental"
    },
    "ReserveCarRental": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-west-2:123456789012:function:ReserveCarRental",
        "Payload": {
          "bookingId.$": "$.bookingId",
          "carRentalDetails.$": "$.carRentalDetails",
          "customerId.$": "$.customerId"
        }
      },
      "ResultPath": "$.carRentalReservation",
      "Catch": [
        {
          "ErrorEquals": ["States.ALL"],
          "ResultPath": "$.error",
          "Next": "CancelHotelReservation"
        }
      ],
      "Next": "ProcessPayment"
    },
    "ProcessPayment": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-west-2:123456789012:function:ProcessPayment",
        "Payload": {
          "bookingId.$": "$.bookingId",
          "customerId.$": "$.customerId",
          "amount.$": "$.totalAmount",
          "paymentDetails.$": "$.paymentDetails"
        }
      },
      "ResultPath": "$.paymentResult",
      "Catch": [
        {
          "ErrorEquals": ["States.ALL"],
          "ResultPath": "$.error",
          "Next": "CancelCarRentalReservation"
        }
      ],
      "Next": "ConfirmBooking"
    },
    "ConfirmBooking": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-west-2:123456789012:function:ConfirmBooking",
        "Payload": {
          "bookingId.$": "$.bookingId",
          "customerId.$": "$.customerId",
          "flightReservation.$": "$.flightReservation",
          "hotelReservation.$": "$.hotelReservation",
          "carRentalReservation.$": "$.carRentalReservation",
          "paymentResult.$": "$.paymentResult"
        }
      },
      "ResultPath": "$.bookingConfirmation",
      "Next": "SendNotification"
    },
    "SendNotification": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-west-2:123456789012:function:SendNotification",
        "Payload": {
          "bookingId.$": "$.bookingId",
          "customerId.$": "$.customerId",
          "notificationType": "BOOKING_CONFIRMED",
          "bookingDetails.$": "$.bookingConfirmation"
        }
      },
      "ResultPath": "$.notification",
      "End": true
    },
    "CancelFlightReservation": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-west-2:123456789012:function:CancelFlightReservation",
        "Payload": {
          "bookingId.$": "$.bookingId",
          "flightReservation.$": "$.flightReservation"
        }
      },
      "ResultPath": "$.flightCancellation",
      "Next": "BookingFailed"
    },
    "CancelHotelReservation": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-west-2:123456789012:function:CancelHotelReservation",
        "Payload": {
          "bookingId.$": "$.bookingId",
          "hotelReservation.$": "$.hotelReservation"
        }
      },
      "ResultPath": "$.hotelCancellation",
      "Next": "CancelFlightReservation"
    },
    "CancelCarRentalReservation": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-west-2:123456789012:function:CancelCarRentalReservation",
        "Payload": {
          "bookingId.$": "$.bookingId",
          "carRentalReservation.$": "$.carRentalReservation"
        }
      },
      "ResultPath": "$.carRentalCancellation",
      "Next": "CancelHotelReservation"
    },
    "BookingFailed": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-west-2:123456789012:function:HandleBookingFailure",
        "Payload": {
          "bookingId.$": "$.bookingId",
          "customerId.$": "$.customerId",
          "error.$": "$.error"
        }
      },
      "ResultPath": "$.failureHandling",
      "Next": "SendFailureNotification"
    },
    "SendFailureNotification": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-west-2:123456789012:function:SendNotification",
        "Payload": {
          "bookingId.$": "$.bookingId",
          "customerId.$": "$.customerId",
          "notificationType": "BOOKING_FAILED",
          "error.$": "$.error"
        }
      },
      "ResultPath": "$.notification",
      "End": true
    }
  }
}
```

### Kubernetes Deployment for Booking Orchestrator

```yaml
# booking-orchestrator-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: booking-orchestrator
  namespace: travelnow
spec:
  replicas: 3
  selector:
    matchLabels:
      app: booking-orchestrator
  template:
    metadata:
      labels:
        app: booking-orchestrator
    spec:
      containers:
      - name: booking-orchestrator
        image: travelnow/booking-orchestrator:1.0.0
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "production"
        - name: AWS_REGION
          value: "us-west-2"
        - name: STEP_FUNCTIONS_ARN
          valueFrom:
            configMapKeyRef:
              name: saga-config
              key: step.functions.arn
        - name: DYNAMODB_TABLE
          valueFrom:
            configMapKeyRef:
              name: saga-config
              key: dynamodb.table
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: aws-credentials
              key: access.key.id
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: aws-credentials
              key: secret.access.key
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 15
---
apiVersion: v1
kind: Service
metadata:
  name: booking-orchestrator
  namespace: travelnow
spec:
  selector:
    app: booking-orchestrator
  ports:
  - port: 80
    targetPort: 8080
  type: ClusterIP
```

### Compensation Service Implementation

```java
// FlightCompensationService.java
@Service
public class FlightCompensationService {
    
    private final FlightRepository flightRepository;
    private final AmazonSQS sqsClient;
    private final String compensationQueue;
    private final AmazonCloudWatch cloudWatchClient;
    
    @Autowired
    public FlightCompensationService(
            FlightRepository flightRepository,
            AmazonSQS sqsClient,
            @Value("${aws.sqs.compensation-queue}") String compensationQueue,
            AmazonCloudWatch cloudWatchClient) {
        this.flightRepository = flightRepository;
        this.sqsClient = sqsClient;
        this.compensationQueue = compensationQueue;
        this.cloudWatchClient = cloudWatchClient;
    }
    
    @Transactional
    public void cancelFlightReservation(String bookingId, String reservationId) {
        try {
            // Find the reservation
            FlightReservation reservation = flightRepository.findByReservationId(reservationId)
                .orElseThrow(() -> new ReservationNotFoundException("Flight reservation not found: " + reservationId));
            
            // Check if already cancelled to ensure idempotence
            if (reservation.getStatus() == ReservationStatus.CANCELLED) {
                log.info("Flight reservation {} already cancelled, skipping compensation", reservationId);
                return;
            }
            
            // Update reservation status
            reservation.setStatus(ReservationStatus.CANCELLED);
            reservation.setCancellationReason("Booking saga compensation");
            reservation.setCancelledAt(LocalDateTime.now());
            flightRepository.save(reservation);
            
            // Release inventory
            releaseFlightInventory(reservation.getFlightId(), reservation.getSeatCount());
            
            // Record metric for successful compensation
            emitCompensationMetric("FlightCancellation", "Success");
            
            log.info("Successfully cancelled flight reservation {} for booking {}", 
                    reservationId, bookingId);
        } catch (Exception e) {
            // Record metric for failed compensation
            emitCompensationMetric("FlightCancellation", "Failure");
            
            // Send to dead-letter queue for manual handling
            sendToDeadLetterQueue(bookingId, reservationId, e.getMessage());
            
            log.error("Failed to cancel flight reservation {} for booking {}: {}", 
                    reservationId, bookingId, e.getMessage(), e);
            throw new CompensationFailedException("Failed to cancel flight reservation", e);
        }
    }
    
    private void releaseFlightInventory(String flightId, int seatCount) {
        // Implementation to release seats back to inventory
    }
    
    private void sendToDeadLetterQueue(String bookingId, String reservationId, String errorMessage) {
        CompensationFailure failure = new CompensationFailure(
                bookingId,
                "FLIGHT",
                reservationId,
                errorMessage,
                LocalDateTime.now()
        );
        
        sqsClient.sendMessage(new SendMessageRequest()
                .withQueueUrl(compensationQueue)
                .withMessageBody(toJson(failure))
                .withMessageDeduplicationId(bookingId + "-flight-" + reservationId)
                .withMessageGroupId(bookingId));
    }
    
    private void emitCompensationMetric(String compensationType, String result) {
        cloudWatchClient.putMetricData(new PutMetricDataRequest()
                .withNamespace("TravelNow/Saga/Compensation")
                .withMetricData(new MetricDatum()
                        .withMetricName(compensationType + result)
                        .withValue(1.0)
                        .withUnit(StandardUnit.Count)));
    }
    
    private String toJson(Object object) {
        try {
            return new ObjectMapper().writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to serialize to JSON", e);
        }
    }
}
```

## Benefits and Considerations

### Benefits

1. **Service Autonomy**: Each service manages its own data and transactions
2. **Scalability**: Services can scale independently without distributed locks
3. **Resilience**: System can recover from partial failures
4. **Visibility**: Clear tracking of transaction progress and state
5. **Flexibility**: Services can use different technologies and data stores
6. **Maintainability**: Services can evolve independently
7. **Fault Isolation**: Failures are contained and don't affect the entire system

### Considerations and Challenges

1. **Complexity**: More complex than traditional transactions
2. **Eventual Consistency**: System may be temporarily inconsistent during saga execution
3. **Idempotency**: Services must handle duplicate requests
4. **Compensation Design**: Designing effective compensating transactions
5. **Monitoring**: Need for comprehensive monitoring and alerting
6. **Testing**: More complex testing scenarios
7. **Debugging**: Distributed nature makes debugging more challenging

## Conclusion

By implementing the Saga Pattern, TravelNow successfully modernized their booking system into a microservices architecture while maintaining data consistency across services. The orchestration-based saga approach provided a clear and manageable way to coordinate the distributed transaction while ensuring proper compensation in case of failures.

Key improvements included:
- 99.9% booking transaction reliability
- 70% reduction in booking processing time
- Ability to scale individual services based on demand
- Clear visibility into the booking process for customer support
- Improved resilience with automatic compensation for failures
- Flexibility for different teams to work on different services

The saga-based architecture also provided a solid foundation for adding new services and features to the booking process, such as travel insurance, activity bookings, and loyalty program integration.

## References

1. Chris Richardson: [Saga Pattern](https://microservices.io/patterns/data/saga.html)
2. AWS Step Functions: [Managing Distributed Transactions](https://aws.amazon.com/step-functions/use-cases/#Managing_Distributed_Transactions)
3. Kubernetes Documentation: [Deployments](https://kubernetes.io/docs/concepts/workloads/controllers/deployment/)
4. AWS Architecture Blog: [Implementing Microservices on AWS](https://aws.amazon.com/blogs/architecture/category/application-services/microservices/)
