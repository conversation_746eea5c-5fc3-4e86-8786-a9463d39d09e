# Scatter-Gather Pattern Case Study

## Overview

The Scatter-Gather Pattern involves broadcasting a request to multiple recipients and then aggregating their responses, useful for parallel processing and collecting comprehensive results. This case study examines a real-world implementation in a travel booking aggregation platform.

## Business Context

**Company**: TravelHub - A travel booking comparison platform
**Challenge**: Aggregating flight, hotel, and car rental options from multiple providers in real-time
**Goal**: Provide customers with comprehensive travel options by querying multiple suppliers simultaneously and presenting unified results

## System Architecture

### Components Involved
- **Search Aggregator**: Central component that coordinates scatter-gather operations
- **Flight Services**: Multiple airline APIs (American, Delta, United, Southwest, etc.)
- **Hotel Services**: Multiple hotel booking APIs (Booking.com, Expedia, Hotels.com, etc.)
- **Car Rental Services**: Multiple car rental APIs (Hertz, Avis, Enterprise, etc.)
- **Result Processor**: Aggregates and normalizes responses from different providers
- **Cache Service**: Stores frequently requested results
- **Rate Limiter**: Manages API call quotas for different providers

### Scatter-Gather Flow
```
Customer searches for "NYC to LAX, Dec 15-20"
    ↓
Search Aggregator receives request
    ↓
SCATTER PHASE:
    • Request sent to 8 flight APIs simultaneously
    • Request sent to 12 hotel APIs simultaneously
    • Request sent to 6 car rental APIs simultaneously
    ↓
PROCESSING PHASE:
    • Each provider processes request independently
    • Providers return results at different times
    • Some providers may timeout or fail
    ↓
GATHER PHASE:
    • Collect responses as they arrive
    • Wait for all responses or timeout (5 seconds)
    • Handle partial failures gracefully
    ↓
AGGREGATION PHASE:
    • Normalize data formats across providers
    • Remove duplicates and invalid results
    • Sort by price, rating, and relevance
    • Apply business rules and filters
    ↓
Present unified results to customer
```

## Implementation Details

### Technology Stack
- **Aggregator Service**: Spring Boot with WebFlux (reactive programming)
- **HTTP Client**: WebClient with connection pooling
- **Caching**: Redis for result caching
- **Message Queue**: RabbitMQ for async processing
- **Database**: MongoDB for provider configurations
- **Monitoring**: Micrometer with Prometheus

### Core Implementation

**Search Aggregator Service**:
```java
@Service
public class TravelSearchAggregator {

    private final List<FlightProvider> flightProviders;
    private final List<HotelProvider> hotelProviders;
    private final List<CarRentalProvider> carRentalProviders;
    private final ResultProcessor resultProcessor;
    private final CacheService cacheService;

    public Mono<TravelSearchResults> searchTravel(TravelSearchRequest request) {
        String cacheKey = generateCacheKey(request);

        // Check cache first
        return cacheService.get(cacheKey, TravelSearchResults.class)
            .switchIfEmpty(performLiveSearch(request, cacheKey));
    }

    private Mono<TravelSearchResults> performLiveSearch(TravelSearchRequest request, String cacheKey) {
        // Scatter phase - send requests to all providers
        Mono<List<FlightResult>> flightResults = scatterFlightSearch(request);
        Mono<List<HotelResult>> hotelResults = scatterHotelSearch(request);
        Mono<List<CarRentalResult>> carResults = scatterCarRentalSearch(request);

        // Gather phase - combine all results
        return Mono.zip(flightResults, hotelResults, carResults)
            .map(tuple -> {
                List<FlightResult> flights = tuple.getT1();
                List<HotelResult> hotels = tuple.getT2();
                List<CarRentalResult> cars = tuple.getT3();

                // Aggregate and process results
                TravelSearchResults results = resultProcessor.aggregateResults(
                    flights, hotels, cars, request
                );

                // Cache results for future requests
                cacheService.put(cacheKey, results, Duration.ofMinutes(15));

                return results;
            })
            .timeout(Duration.ofSeconds(8)) // Overall timeout
            .onErrorReturn(createFallbackResults(request));
    }

    private Mono<List<FlightResult>> scatterFlightSearch(TravelSearchRequest request) {
        List<Mono<FlightResult>> flightSearches = flightProviders.stream()
            .map(provider -> searchFlightProvider(provider, request))
            .collect(Collectors.toList());

        // Wait for all responses or timeout after 5 seconds
        return Flux.merge(flightSearches)
            .timeout(Duration.ofSeconds(5))
            .onErrorContinue((error, item) -> {
                log.warn("Flight provider failed: {}", error.getMessage());
                // Continue with other providers even if one fails
            })
            .collectList();
    }

    private Mono<FlightResult> searchFlightProvider(FlightProvider provider, TravelSearchRequest request) {
        return provider.search(request)
            .timeout(Duration.ofSeconds(3)) // Per-provider timeout
            .onErrorReturn(FlightResult.empty()) // Return empty result on failure
            .doOnSuccess(result -> recordProviderMetrics(provider.getName(), true))
            .doOnError(error -> recordProviderMetrics(provider.getName(), false));
    }

    private Mono<List<HotelResult>> scatterHotelSearch(TravelSearchRequest request) {
        // Similar implementation to flight search
        List<Mono<HotelResult>> hotelSearches = hotelProviders.stream()
            .map(provider -> searchHotelProvider(provider, request))
            .collect(Collectors.toList());

        return Flux.merge(hotelSearches)
            .timeout(Duration.ofSeconds(5))
            .onErrorContinue((error, item) -> {
                log.warn("Hotel provider failed: {}", error.getMessage());
            })
            .collectList();
    }

    private Mono<List<CarRentalResult>> scatterCarRentalSearch(TravelSearchRequest request) {
        // Similar implementation to flight search
        List<Mono<CarRentalResult>> carSearches = carRentalProviders.stream()
            .map(provider -> searchCarRentalProvider(provider, request))
            .collect(Collectors.toList());

        return Flux.merge(carSearches)
            .timeout(Duration.ofSeconds(5))
            .onErrorContinue((error, item) -> {
                log.warn("Car rental provider failed: {}", error.getMessage());
            })
            .collectList();
    }
}
```

### Result Processing and Aggregation

**Result Processor**:
```java
@Component
public class TravelResultProcessor {

    public TravelSearchResults aggregateResults(List<FlightResult> flights,
                                              List<HotelResult> hotels,
                                              List<CarRentalResult> cars,
                                              TravelSearchRequest request) {

        // Normalize and filter flight results
        List<NormalizedFlight> normalizedFlights = flights.stream()
            .filter(result -> !result.isEmpty())
            .flatMap(result -> result.getFlights().stream())
            .map(this::normalizeFlightData)
            .filter(flight -> isValidFlight(flight, request))
            .sorted(Comparator.comparing(NormalizedFlight::getPrice))
            .limit(50) // Limit results for performance
            .collect(Collectors.toList());

        // Normalize and filter hotel results
        List<NormalizedHotel> normalizedHotels = hotels.stream()
            .filter(result -> !result.isEmpty())
            .flatMap(result -> result.getHotels().stream())
            .map(this::normalizeHotelData)
            .filter(hotel -> isValidHotel(hotel, request))
            .sorted(Comparator.comparing(NormalizedHotel::getPricePerNight))
            .limit(50)
            .collect(Collectors.toList());

        // Normalize and filter car rental results
        List<NormalizedCarRental> normalizedCars = cars.stream()
            .filter(result -> !result.isEmpty())
            .flatMap(result -> result.getCars().stream())
            .map(this::normalizeCarRentalData)
            .filter(car -> isValidCarRental(car, request))
            .sorted(Comparator.comparing(NormalizedCarRental::getDailyRate))
            .limit(30)
            .collect(Collectors.toList());

        // Create travel packages by combining flights, hotels, and cars
        List<TravelPackage> packages = createTravelPackages(
            normalizedFlights, normalizedHotels, normalizedCars, request
        );

        return TravelSearchResults.builder()
            .flights(normalizedFlights)
            .hotels(normalizedHotels)
            .carRentals(normalizedCars)
            .packages(packages)
            .searchMetadata(createSearchMetadata(flights, hotels, cars))
            .build();
    }

    private NormalizedFlight normalizeFlightData(Flight flight) {
        return NormalizedFlight.builder()
            .airline(flight.getAirline())
            .flightNumber(flight.getFlightNumber())
            .departure(normalizeAirport(flight.getDeparture()))
            .arrival(normalizeAirport(flight.getArrival()))
            .price(normalizeCurrency(flight.getPrice()))
            .duration(normalizeDuration(flight.getDuration()))
            .stops(flight.getStops())
            .provider(flight.getProvider())
            .bookingUrl(flight.getBookingUrl())
            .build();
    }

    private SearchMetadata createSearchMetadata(List<FlightResult> flights,
                                              List<HotelResult> hotels,
                                              List<CarRentalResult> cars) {
        int totalProviders = flights.size() + hotels.size() + cars.size();
        int successfulProviders = (int) (flights.stream().filter(r -> !r.isEmpty()).count() +
                                        hotels.stream().filter(r -> !r.isEmpty()).count() +
                                        cars.stream().filter(r -> !r.isEmpty()).count());

        return SearchMetadata.builder()
            .totalProvidersQueried(totalProviders)
            .successfulProviders(successfulProviders)
            .searchDuration(Duration.ofMillis(System.currentTimeMillis() - searchStartTime))
            .cacheHit(false)
            .build();
    }
}

## Challenges and Solutions

### Challenge 1: Varying Response Times
**Problem**: Different providers have vastly different response times (100ms to 5+ seconds), affecting user experience.

**Solution**:
- Implemented progressive result display - show results as they arrive
- Set aggressive timeouts per provider (3 seconds) and overall timeout (8 seconds)
- Used reactive programming to handle asynchronous responses efficiently
- Cached frequently requested routes to reduce provider calls

### Challenge 2: Provider Reliability Issues
**Problem**: Some providers frequently timeout, return errors, or have rate limits.

**Solution**:
- Implemented circuit breaker pattern for unreliable providers
- Added provider health monitoring and automatic disabling of failing providers
- Created fallback mechanisms to continue with partial results
- Implemented retry logic with exponential backoff for transient failures

### Challenge 3: Data Format Inconsistencies
**Problem**: Each provider returns data in different formats, currencies, and structures.

**Solution**:
- Built comprehensive data normalization layer
- Created provider-specific adapters to handle format differences
- Implemented currency conversion service for price normalization
- Added data validation to filter out invalid or incomplete results

### Challenge 4: Rate Limiting and API Quotas
**Problem**: Providers impose strict rate limits and daily quotas on API calls.

**Solution**:
- Implemented intelligent caching strategy with 15-minute TTL for popular routes
- Added rate limiting per provider to stay within quotas
- Created provider rotation logic to distribute load
- Implemented request deduplication to avoid unnecessary API calls

## Results and Benefits

### Performance Improvements
- **Response Time**: Average search time reduced from 12 seconds to 3.5 seconds
- **Success Rate**: Achieved 95% search success rate despite individual provider failures
- **Throughput**: Increased concurrent search capacity by 400%
- **Cache Hit Rate**: Achieved 60% cache hit rate for popular routes

### Business Benefits
- **Comprehensive Results**: Customers see options from 25+ providers in a single search
- **Competitive Pricing**: Real-time price comparison leads to better deals for customers
- **Availability**: System remains functional even when multiple providers are down
- **Market Coverage**: Expanded from 3 providers to 25+ providers without architectural changes

### User Experience Benefits
- **Speed**: Results appear progressively as providers respond
- **Reliability**: Consistent search results even during provider outages
- **Completeness**: More travel options lead to higher booking conversion rates
- **Transparency**: Users can see which providers were searched and their response status

## Lessons Learned

### What Worked Well
1. **Reactive Programming**: Spring WebFlux handled concurrent requests efficiently
2. **Progressive Results**: Showing results as they arrive improved perceived performance
3. **Circuit Breakers**: Prevented cascading failures from unreliable providers
4. **Comprehensive Caching**: Dramatically reduced API calls and improved response times

### What Could Be Improved
1. **Provider Prioritization**: Should have implemented provider ranking based on historical performance
2. **Smart Timeouts**: Fixed timeouts weren't optimal - should be dynamic based on provider history
3. **Result Quality Scoring**: Initially didn't score result quality, leading to some poor recommendations
4. **Monitoring Granularity**: Needed more detailed metrics per provider and route

## Best Practices Identified

1. **Design for Partial Failure**: System should work even when most providers fail
2. **Implement Progressive Display**: Show results as they arrive rather than waiting for all
3. **Use Aggressive Timeouts**: Better to show partial results quickly than wait for slow providers
4. **Cache Intelligently**: Cache popular queries but ensure data freshness for pricing
5. **Monitor Provider Health**: Automatically disable failing providers and re-enable when recovered
6. **Normalize Data Early**: Convert all data to common format as soon as it's received

## When to Use Scatter-Gather Pattern

### Good Fit When:
- Need to aggregate data from multiple independent sources
- Sources have varying response times and reliability
- Partial results are acceptable and valuable
- High availability is more important than complete consistency
- Sources provide similar types of data that can be meaningfully combined

### Not Suitable When:
- All sources must respond for the operation to be valid
- Sources are highly interdependent
- Response time requirements are very strict (sub-100ms)
- Data from different sources cannot be meaningfully aggregated
- Network latency to sources is very high

## Performance Optimization Techniques

### 1. Connection Pooling
```java
@Configuration
public class HttpClientConfig {

    @Bean
    public WebClient webClient() {
        ConnectionProvider provider = ConnectionProvider.builder("travel-providers")
            .maxConnections(500)
            .maxIdleTime(Duration.ofSeconds(20))
            .maxLifeTime(Duration.ofSeconds(60))
            .pendingAcquireTimeout(Duration.ofSeconds(5))
            .build();

        HttpClient httpClient = HttpClient.create(provider)
            .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000)
            .responseTimeout(Duration.ofSeconds(5));

        return WebClient.builder()
            .clientConnector(new ReactorClientHttpConnector(httpClient))
            .build();
    }
}
```

### 2. Intelligent Caching Strategy
```java
@Component
public class SmartCacheService {

    public Mono<TravelSearchResults> getOrSearch(TravelSearchRequest request) {
        String cacheKey = generateCacheKey(request);

        // Different TTL based on route popularity and time sensitivity
        Duration ttl = calculateOptimalTTL(request);

        return cacheService.get(cacheKey, TravelSearchResults.class)
            .filter(results -> !isStale(results, request))
            .switchIfEmpty(performSearchAndCache(request, cacheKey, ttl));
    }

    private Duration calculateOptimalTTL(TravelSearchRequest request) {
        if (isPopularRoute(request)) {
            return Duration.ofMinutes(5); // Shorter TTL for popular routes
        } else if (isAdvanceBooking(request)) {
            return Duration.ofHours(2); // Longer TTL for advance bookings
        } else {
            return Duration.ofMinutes(15); // Default TTL
        }
    }
}
```

## Conclusion

The Scatter-Gather pattern enabled TravelHub to create a comprehensive travel search platform that aggregates results from dozens of providers while maintaining excellent performance and reliability. The key to success was embracing partial failures, implementing progressive result display, and building robust error handling.

The pattern works exceptionally well for scenarios where you need to aggregate similar data from multiple independent sources, especially when those sources have varying reliability and performance characteristics. The investment in proper timeout handling, caching, and result normalization pays dividends in both performance and user experience.