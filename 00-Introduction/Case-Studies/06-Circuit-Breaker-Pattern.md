# Circuit Breaker Pattern: Resilient Payment Processing System

## Problem Statement

**PaySecure**, a financial technology company, operates a payment processing platform that integrates with multiple payment providers, banks, and financial institutions. The system faces several challenges:

1. External payment gateways and banking APIs occasionally experience outages or degraded performance
2. Failed API calls lead to increased latency and resource consumption
3. Timeouts and retries create cascading failures during provider outages
4. Customer transactions get stuck in pending states during service disruptions
5. System resources become exhausted when external services are slow to respond
6. Recovery from outages is slow and requires manual intervention
7. Monitoring and alerting for service health is reactive rather than proactive

The engineering team needs to implement a solution that makes the payment system resilient to external service failures while maintaining high availability.

## Solution: Circuit Breaker Pattern with AWS and Kubernetes

The team implements the Circuit Breaker pattern to detect failures and prevent the application from performing operations that are likely to fail. When a service is experiencing issues, the circuit breaker "trips" and fails fast, allowing the system to degrade gracefully and recover automatically when the service returns to normal.

### Architecture Overview

![Circuit Breaker Pattern Architecture](../images/circuit-breaker-pattern.png)

### Key Components

1. **Payment Processing Services**
   - **Transaction Service**: Handles payment transactions (Amazon EKS)
   - **Provider Gateway Services**: Interfaces with payment providers (Amazon EKS)
   - **Reconciliation Service**: Ensures transaction consistency (Amazon EKS)
   - **Notification Service**: Alerts users about transaction status (AWS Lambda)

2. **Circuit Breaker Implementation**
   - **Resilience4j**: Circuit breaker library integrated into services
   - **Amazon CloudWatch**: Monitors circuit breaker states and metrics
   - **AWS Lambda**: Handles fallback mechanisms and recovery
   - **Amazon EventBridge**: Routes events for circuit state changes

3. **Fallback Mechanisms**
   - **Alternative Provider Routing**: Routes to backup providers when primary fails
   - **Asynchronous Processing**: Queues transactions for later processing
   - **Degraded Mode Operations**: Allows essential operations with reduced functionality

4. **Infrastructure**
   - **Amazon EKS**: Orchestrates containerized services
   - **Amazon SQS**: Queues for asynchronous processing
   - **Amazon DynamoDB**: Stores circuit breaker state and transaction data
   - **AWS X-Ray**: Provides distributed tracing

### Implementation Strategy

#### Phase 1: Circuit Breaker Implementation

1. **Library Integration**
   - Integrate Resilience4j into payment services
   - Configure circuit breaker parameters for each external dependency
   - Implement fallback mechanisms for each service
   - Set up metrics collection for circuit breaker states

2. **Circuit Configuration**
   - Define failure thresholds based on error rates and response times
   - Configure circuit half-open retry parameters
   - Set up automatic recovery mechanisms
   - Implement circuit state persistence

#### Phase 2: Fallback Strategy Implementation

1. **Alternative Provider Routing**
   - Implement provider ranking and selection logic
   - Create configuration for provider failover sequences
   - Develop provider health checking mechanisms
   - Set up provider performance metrics

2. **Asynchronous Processing**
   - Implement SQS queues for delayed processing
   - Create background processors for queued transactions
   - Develop retry strategies with exponential backoff
   - Implement dead-letter queues for failed transactions

#### Phase 3: Monitoring and Alerting

1. **Metrics Dashboard**
   - Create CloudWatch dashboards for circuit breaker states
   - Set up alerts for circuit open events
   - Implement service health visualization
   - Create provider performance dashboards

2. **Operational Procedures**
   - Develop runbooks for circuit breaker events
   - Create automated recovery procedures
   - Implement provider communication protocols
   - Set up on-call rotation for critical alerts

#### Phase 4: Testing and Validation

1. **Chaos Testing**
   - Implement chaos engineering practices
   - Simulate provider failures and latency
   - Test recovery mechanisms
   - Validate fallback behavior

2. **Load Testing**
   - Test system behavior under high load
   - Validate circuit breaker thresholds
   - Measure recovery times
   - Verify system stability during provider outages

## Implementation Details

### Circuit Breaker Configuration

```yaml
# circuit-breaker-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: circuit-breaker-config
  namespace: paysecure
data:
  application.yml: |
    resilience4j:
      circuitbreaker:
        configs:
          default:
            registerHealthIndicator: true
            slidingWindowSize: 100
            minimumNumberOfCalls: 10
            permittedNumberOfCallsInHalfOpenState: 5
            automaticTransitionFromOpenToHalfOpenEnabled: true
            waitDurationInOpenState: 30s
            failureRateThreshold: 50
            eventConsumerBufferSize: 10
            recordExceptions:
              - java.io.IOException
              - java.net.ConnectException
              - org.springframework.web.client.HttpServerErrorException
          payment-gateway:
            baseConfig: default
            slidingWindowSize: 50
            waitDurationInOpenState: 45s
            failureRateThreshold: 30
          banking-api:
            baseConfig: default
            slidingWindowSize: 20
            waitDurationInOpenState: 60s
            failureRateThreshold: 40
        instances:
          visaGateway:
            baseConfig: payment-gateway
          mastercardGateway:
            baseConfig: payment-gateway
          amexGateway:
            baseConfig: payment-gateway
          bankTransferApi:
            baseConfig: banking-api
          achProcessingApi:
            baseConfig: banking-api
      retry:
        configs:
          default:
            maxAttempts: 3
            waitDuration: 1s
            enableExponentialBackoff: true
            exponentialBackoffMultiplier: 2
            retryExceptions:
              - java.io.IOException
              - java.net.ConnectException
              - org.springframework.web.client.HttpServerErrorException
        instances:
          visaGateway:
            baseConfig: default
          mastercardGateway:
            baseConfig: default
      bulkhead:
        configs:
          default:
            maxConcurrentCalls: 25
            maxWaitDuration: 2s
        instances:
          visaGateway:
            baseConfig: default
            maxConcurrentCalls: 50
          mastercardGateway:
            baseConfig: default
            maxConcurrentCalls: 40
```

### Payment Service with Circuit Breaker

```java
// PaymentGatewayService.java
@Service
public class PaymentGatewayService {
    
    private final RestTemplate restTemplate;
    private final CircuitBreakerRegistry circuitBreakerRegistry;
    private final PaymentQueueService paymentQueueService;
    private final MetricsService metricsService;
    private final ProviderHealthService providerHealthService;
    
    @Autowired
    public PaymentGatewayService(
            RestTemplate restTemplate,
            CircuitBreakerRegistry circuitBreakerRegistry,
            PaymentQueueService paymentQueueService,
            MetricsService metricsService,
            ProviderHealthService providerHealthService) {
        this.restTemplate = restTemplate;
        this.circuitBreakerRegistry = circuitBreakerRegistry;
        this.paymentQueueService = paymentQueueService;
        this.metricsService = metricsService;
        this.providerHealthService = providerHealthService;
    }
    
    public PaymentResponse processPayment(PaymentRequest paymentRequest) {
        String primaryProvider = selectPrimaryProvider(paymentRequest);
        String circuitBreakerName = primaryProvider + "Gateway";
        
        CircuitBreaker circuitBreaker = circuitBreakerRegistry.circuitBreaker(circuitBreakerName);
        
        // Create a circuit breaker decorated function
        Supplier<PaymentResponse> paymentSupplier = CircuitBreaker.decorateSupplier(
                circuitBreaker,
                () -> callPaymentProvider(primaryProvider, paymentRequest)
        );
        
        try {
            // Execute the decorated function
            return Try.ofSupplier(paymentSupplier)
                    .recover(this::handleFailure)
                    .get();
        } catch (Exception e) {
            log.error("Payment processing failed for all providers", e);
            return createFailureResponse(paymentRequest, "All payment providers unavailable");
        }
    }
    
    private PaymentResponse callPaymentProvider(String provider, PaymentRequest request) {
        String providerUrl = getProviderUrl(provider);
        log.info("Calling payment provider: {} for transaction {}", provider, request.getTransactionId());
        
        long startTime = System.currentTimeMillis();
        try {
            PaymentResponse response = restTemplate.postForObject(
                    providerUrl,
                    request,
                    PaymentResponse.class
            );
            
            long duration = System.currentTimeMillis() - startTime;
            metricsService.recordProviderLatency(provider, duration);
            metricsService.incrementSuccessCounter(provider);
            
            log.info("Payment provider {} responded successfully in {}ms", provider, duration);
            return response;
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            metricsService.recordProviderLatency(provider, duration);
            metricsService.incrementFailureCounter(provider);
            
            log.error("Payment provider {} failed in {}ms: {}", provider, duration, e.getMessage());
            throw e;
        }
    }
    
    private PaymentResponse handleFailure(Throwable throwable) {
        if (throwable instanceof CallNotPermittedException) {
            // Circuit is open, try alternative provider
            log.warn("Circuit open for primary provider, trying alternative provider");
            return tryAlternativeProvider(paymentRequest);
        } else {
            // Other failure, try alternative provider
            log.error("Primary provider failed: {}", throwable.getMessage());
            return tryAlternativeProvider(paymentRequest);
        }
    }
    
    private PaymentResponse tryAlternativeProvider(PaymentRequest paymentRequest) {
        List<String> alternativeProviders = getAlternativeProviders(paymentRequest);
        
        for (String provider : alternativeProviders) {
            String circuitBreakerName = provider + "Gateway";
            CircuitBreaker circuitBreaker = circuitBreakerRegistry.circuitBreaker(circuitBreakerName);
            
            if (circuitBreaker.getState() == CircuitBreaker.State.CLOSED || 
                circuitBreaker.getState() == CircuitBreaker.State.HALF_OPEN) {
                try {
                    log.info("Trying alternative provider: {}", provider);
                    return callPaymentProvider(provider, paymentRequest);
                } catch (Exception e) {
                    log.warn("Alternative provider {} failed: {}", provider, e.getMessage());
                    // Continue to next provider
                }
            }
        }
        
        // All providers failed, queue for later processing
        log.warn("All providers failed, queueing payment for later processing");
        paymentQueueService.queuePayment(paymentRequest);
        
        return createDeferredResponse(paymentRequest);
    }
    
    private String selectPrimaryProvider(PaymentRequest paymentRequest) {
        // Logic to select the best provider based on card type, amount, region, etc.
        if (paymentRequest.getCardType().equals("VISA")) {
            return "visa";
        } else if (paymentRequest.getCardType().equals("MASTERCARD")) {
            return "mastercard";
        } else if (paymentRequest.getCardType().equals("AMEX")) {
            return "amex";
        } else {
            return "visa"; // Default
        }
    }
    
    private List<String> getAlternativeProviders(PaymentRequest paymentRequest) {
        // Return alternative providers in priority order
        String primaryProvider = selectPrimaryProvider(paymentRequest);
        List<String> allProviders = Arrays.asList("visa", "mastercard", "amex");
        
        return allProviders.stream()
                .filter(p -> !p.equals(primaryProvider))
                .filter(p -> providerHealthService.isProviderHealthy(p))
                .collect(Collectors.toList());
    }
    
    private PaymentResponse createDeferredResponse(PaymentRequest paymentRequest) {
        PaymentResponse response = new PaymentResponse();
        response.setTransactionId(paymentRequest.getTransactionId());
        response.setStatus("DEFERRED");
        response.setMessage("Payment processing delayed due to provider unavailability");
        return response;
    }
    
    private PaymentResponse createFailureResponse(PaymentRequest paymentRequest, String message) {
        PaymentResponse response = new PaymentResponse();
        response.setTransactionId(paymentRequest.getTransactionId());
        response.setStatus("FAILED");
        response.setMessage(message);
        return response;
    }
}
```

### Circuit Breaker Metrics Collection

```java
// CircuitBreakerMetricsCollector.java
@Component
public class CircuitBreakerMetricsCollector {
    
    private final CircuitBreakerRegistry circuitBreakerRegistry;
    private final AmazonCloudWatch cloudWatchClient;
    private final String namespace;
    
    @Autowired
    public CircuitBreakerMetricsCollector(
            CircuitBreakerRegistry circuitBreakerRegistry,
            AmazonCloudWatch cloudWatchClient,
            @Value("${metrics.namespace}") String namespace) {
        this.circuitBreakerRegistry = circuitBreakerRegistry;
        this.cloudWatchClient = cloudWatchClient;
        this.namespace = namespace;
        
        // Register event consumers for all circuit breakers
        circuitBreakerRegistry.getAllCircuitBreakers().forEach(this::registerEventConsumer);
    }
    
    private void registerEventConsumer(CircuitBreaker circuitBreaker) {
        String name = circuitBreaker.getName();
        
        // State transition events
        circuitBreaker.getEventPublisher()
            .onStateTransition(event -> {
                log.info("Circuit breaker '{}' state changed from {} to {}", 
                        name, event.getStateTransition().getFromState(), event.getStateTransition().getToState());
                
                // Publish state change metric
                publishStateMetric(name, event.getStateTransition().getToState().name());
                
                // If circuit opened, publish detailed metrics
                if (event.getStateTransition().getToState() == CircuitBreaker.State.OPEN) {
                    publishCircuitOpenMetrics(circuitBreaker);
                }
            });
        
        // Success and failure events
        circuitBreaker.getEventPublisher()
            .onSuccess(event -> publishCallResultMetric(name, "SUCCESS", event.getElapsedDuration().toMillis()))
            .onError(event -> {
                publishCallResultMetric(name, "FAILURE", event.getElapsedDuration().toMillis());
                log.warn("Circuit breaker '{}' recorded failure: {}", name, event.getThrowable().getMessage());
            });
    }
    
    private void publishStateMetric(String circuitBreakerName, String state) {
        MetricDatum datum = new MetricDatum()
                .withMetricName("CircuitBreakerState")
                .withDimensions(
                        new Dimension().withName("CircuitBreaker").withValue(circuitBreakerName),
                        new Dimension().withName("State").withValue(state)
                )
                .withValue(1.0)
                .withUnit(StandardUnit.Count)
                .withTimestamp(new Date());
        
        cloudWatchClient.putMetricData(new PutMetricDataRequest()
                .withNamespace(namespace)
                .withMetricData(datum));
    }
    
    private void publishCallResultMetric(String circuitBreakerName, String result, long durationMs) {
        // Publish call count
        MetricDatum countDatum = new MetricDatum()
                .withMetricName("CircuitBreakerCalls")
                .withDimensions(
                        new Dimension().withName("CircuitBreaker").withValue(circuitBreakerName),
                        new Dimension().withName("Result").withValue(result)
                )
                .withValue(1.0)
                .withUnit(StandardUnit.Count)
                .withTimestamp(new Date());
        
        // Publish call duration
        MetricDatum durationDatum = new MetricDatum()
                .withMetricName("CircuitBreakerCallDuration")
                .withDimensions(
                        new Dimension().withName("CircuitBreaker").withValue(circuitBreakerName),
                        new Dimension().withName("Result").withValue(result)
                )
                .withValue((double) durationMs)
                .withUnit(StandardUnit.Milliseconds)
                .withTimestamp(new Date());
        
        cloudWatchClient.putMetricData(new PutMetricDataRequest()
                .withNamespace(namespace)
                .withMetricData(countDatum, durationDatum));
    }
    
    private void publishCircuitOpenMetrics(CircuitBreaker circuitBreaker) {
        String name = circuitBreaker.getName();
        CircuitBreaker.Metrics metrics = circuitBreaker.getMetrics();
        
        List<MetricDatum> metricData = new ArrayList<>();
        
        // Failure rate
        metricData.add(new MetricDatum()
                .withMetricName("FailureRate")
                .withDimensions(new Dimension().withName("CircuitBreaker").withValue(name))
                .withValue(metrics.getFailureRate())
                .withUnit(StandardUnit.Percent));
        
        // Number of failed calls
        metricData.add(new MetricDatum()
                .withMetricName("NumberOfFailedCalls")
                .withDimensions(new Dimension().withName("CircuitBreaker").withValue(name))
                .withValue((double) metrics.getNumberOfFailedCalls())
                .withUnit(StandardUnit.Count));
        
        // Number of successful calls
        metricData.add(new MetricDatum()
                .withMetricName("NumberOfSuccessfulCalls")
                .withDimensions(new Dimension().withName("CircuitBreaker").withValue(name))
                .withValue((double) metrics.getNumberOfSuccessfulCalls())
                .withUnit(StandardUnit.Count));
        
        cloudWatchClient.putMetricData(new PutMetricDataRequest()
                .withNamespace(namespace)
                .withMetricData(metricData));
    }
    
    @Scheduled(fixedRate = 60000) // Every minute
    public void publishDetailedMetrics() {
        circuitBreakerRegistry.getAllCircuitBreakers().forEach(circuitBreaker -> {
            String name = circuitBreaker.getName();
            CircuitBreaker.State state = circuitBreaker.getState();
            CircuitBreaker.Metrics metrics = circuitBreaker.getMetrics();
            
            List<MetricDatum> metricData = new ArrayList<>();
            
            // Current state as numeric value
            metricData.add(new MetricDatum()
                    .withMetricName("CircuitBreakerStateValue")
                    .withDimensions(new Dimension().withName("CircuitBreaker").withValue(name))
                    .withValue(getStateValue(state))
                    .withUnit(StandardUnit.None));
            
            // Detailed metrics
            metricData.add(new MetricDatum()
                    .withMetricName("FailureRate")
                    .withDimensions(new Dimension().withName("CircuitBreaker").withValue(name))
                    .withValue(metrics.getFailureRate())
                    .withUnit(StandardUnit.Percent));
            
            metricData.add(new MetricDatum()
                    .withMetricName("SlowCallRate")
                    .withDimensions(new Dimension().withName("CircuitBreaker").withValue(name))
                    .withValue(metrics.getSlowCallRate())
                    .withUnit(StandardUnit.Percent));
            
            cloudWatchClient.putMetricData(new PutMetricDataRequest()
                    .withNamespace(namespace)
                    .withMetricData(metricData));
        });
    }
    
    private double getStateValue(CircuitBreaker.State state) {
        switch (state) {
            case CLOSED: return 0;
            case OPEN: return 1;
            case HALF_OPEN: return 0.5;
            case DISABLED: return -1;
            case FORCED_OPEN: return 2;
            default: return -2;
        }
    }
}
```

### Kubernetes Deployment

```yaml
# payment-service-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: payment-service
  namespace: paysecure
spec:
  replicas: 5
  selector:
    matchLabels:
      app: payment-service
  template:
    metadata:
      labels:
        app: payment-service
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/path: "/actuator/prometheus"
        prometheus.io/port: "8080"
    spec:
      containers:
      - name: payment-service
        image: paysecure/payment-service:1.0.0
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "production"
        - name: AWS_REGION
          value: "us-west-2"
        - name: SPRING_CONFIG_IMPORT
          value: "configmap:/circuit-breaker-config"
        - name: METRICS_NAMESPACE
          value: "PaySecure/CircuitBreaker"
        - name: SQS_PAYMENT_QUEUE_URL
          valueFrom:
            configMapKeyRef:
              name: payment-config
              key: sqs.payment.queue.url
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: aws-credentials
              key: access.key.id
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: aws-credentials
              key: secret.access.key
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 15
        volumeMounts:
        - name: config-volume
          mountPath: /config
      volumes:
      - name: config-volume
        configMap:
          name: circuit-breaker-config
---
apiVersion: v1
kind: Service
metadata:
  name: payment-service
  namespace: paysecure
spec:
  selector:
    app: payment-service
  ports:
  - port: 80
    targetPort: 8080
  type: ClusterIP
```

## Benefits and Considerations

### Benefits

1. **Fail Fast**: Prevents cascading failures by failing quickly when a service is unavailable
2. **Resource Conservation**: Avoids wasting resources on operations likely to fail
3. **Automatic Recovery**: System automatically tests and recovers when external services return to normal
4. **Graceful Degradation**: Allows the system to operate in degraded mode during partial outages
5. **Improved Resilience**: System can withstand external service failures without overall failure
6. **Better User Experience**: Users receive immediate feedback rather than waiting for timeouts
7. **Operational Visibility**: Clear metrics and alerts for service health and circuit breaker states

### Considerations and Challenges

1. **Configuration Tuning**: Finding the right thresholds for circuit opening and closing
2. **Fallback Design**: Creating effective fallback mechanisms for each service
3. **Testing Complexity**: Testing circuit breaker behavior requires simulating failures
4. **False Positives**: Circuits may open due to temporary issues rather than actual failures
5. **Monitoring Overhead**: Additional monitoring and alerting infrastructure required
6. **Recovery Strategy**: Determining appropriate recovery and retry strategies
7. **Cross-Service Impact**: Understanding how circuit breakers in one service affect others

## Conclusion

By implementing the Circuit Breaker pattern, PaySecure significantly improved the resilience of their payment processing system. The solution allowed the system to detect failures in external services, prevent cascading failures, and recover automatically when services returned to normal.

Key improvements included:
- 99.99% payment processing availability, up from 99.9%
- 95% reduction in cascading failures during provider outages
- 80% faster recovery from external service disruptions
- 70% reduction in resource consumption during outages
- Improved visibility into external service health and performance
- Better user experience with immediate feedback during service disruptions
- Reduced operational overhead for handling provider outages

The circuit breaker implementation also provided a foundation for more advanced resilience patterns, such as bulkheading, rate limiting, and adaptive concurrency control, further enhancing the system's ability to handle external service failures.

## References

1. Michael Nygard: [Circuit Breaker Pattern](https://martinfowler.com/bliki/CircuitBreaker.html)
2. Resilience4j: [Documentation](https://resilience4j.readme.io/docs/circuitbreaker)
3. AWS Architecture Blog: [Implementing Circuit Breakers with AWS Lambda](https://aws.amazon.com/blogs/architecture/using-circuit-breakers-to-improve-distributed-system-resilience/)
4. Kubernetes Documentation: [Liveness and Readiness Probes](https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/)
