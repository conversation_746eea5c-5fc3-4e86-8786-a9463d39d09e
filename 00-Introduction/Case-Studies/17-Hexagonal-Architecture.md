# Hexagonal Architecture Case Study

## Overview

Hexagonal Architecture (Ports and Adapters) isolates core business logic from external concerns by defining ports (interfaces) and adapters (implementations). This case study examines implementation at a payment processing company.

## Business Context

**Company**: PayFlow - A payment processing service
**Challenge**: Tight coupling between business logic and external systems (databases, payment gateways, messaging)
**Goal**: Create flexible architecture that allows easy testing and integration with different external systems

## Architecture Implementation

### Core Domain (Center of Hexagon)
```java
// Domain Model
public class Payment {
    private PaymentId id;
    private Money amount;
    private Currency currency;
    private PaymentMethod method;
    private PaymentStatus status;
    private MerchantId merchantId;
    private CustomerId customerId;

    public PaymentResult process(PaymentGateway gateway, FraudDetector fraudDetector) {
        // Core business logic
        if (fraudDetector.isSuspicious(this)) {
            this.status = PaymentStatus.FLAGGED_FOR_REVIEW;
            return PaymentResult.flagged("Suspicious activity detected");
        }

        PaymentGatewayResult result = gateway.processPayment(this);

        if (result.isSuccessful()) {
            this.status = PaymentStatus.COMPLETED;
            return PaymentResult.success(result.getTransactionId());
        } else {
            this.status = PaymentStatus.FAILED;
            return PaymentResult.failed(result.getErrorMessage());
        }
    }
}

// Domain Service
@Service
public class PaymentService {

    private final PaymentRepository paymentRepository;
    private final PaymentGateway paymentGateway;
    private final FraudDetector fraudDetector;
    private final NotificationService notificationService;

    public PaymentResult processPayment(ProcessPaymentCommand command) {
        Payment payment = Payment.create(command);

        PaymentResult result = payment.process(paymentGateway, fraudDetector);

        paymentRepository.save(payment);

        if (result.isSuccessful()) {
            notificationService.sendPaymentConfirmation(payment);
        } else {
            notificationService.sendPaymentFailureNotification(payment, result.getErrorMessage());
        }

        return result;
    }
}
```

### Ports (Interfaces)
```java
// Driven Ports (called by domain)
public interface PaymentRepository {
    void save(Payment payment);
    Optional<Payment> findById(PaymentId id);
    List<Payment> findByMerchantId(MerchantId merchantId);
}

public interface PaymentGateway {
    PaymentGatewayResult processPayment(Payment payment);
    RefundResult refundPayment(PaymentId paymentId, Money amount);
}

public interface FraudDetector {
    boolean isSuspicious(Payment payment);
    FraudScore calculateRiskScore(Payment payment);
}

public interface NotificationService {
    void sendPaymentConfirmation(Payment payment);
    void sendPaymentFailureNotification(Payment payment, String reason);
}

// Driving Ports (call the domain)
public interface PaymentProcessor {
    PaymentResult processPayment(ProcessPaymentCommand command);
    RefundResult refundPayment(RefundCommand command);
    PaymentStatus getPaymentStatus(PaymentId paymentId);
}
```

### Adapters (Implementations)

**Database Adapter**:
```java
@Repository
public class JpaPaymentRepository implements PaymentRepository {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public void save(Payment payment) {
        PaymentEntity entity = PaymentMapper.toEntity(payment);
        entityManager.persist(entity);
    }

    @Override
    public Optional<Payment> findById(PaymentId id) {
        PaymentEntity entity = entityManager.find(PaymentEntity.class, id.getValue());
        return Optional.ofNullable(entity)
            .map(PaymentMapper::toDomain);
    }
}
```

**Payment Gateway Adapter**:
```java
@Component
public class StripePaymentGateway implements PaymentGateway {

    private final StripeClient stripeClient;

    @Override
    public PaymentGatewayResult processPayment(Payment payment) {
        try {
            PaymentIntentCreateParams params = PaymentIntentCreateParams.builder()
                .setAmount(payment.getAmount().getCents())
                .setCurrency(payment.getCurrency().getCode())
                .setPaymentMethod(payment.getMethod().getStripeId())
                .setConfirm(true)
                .build();

            PaymentIntent intent = PaymentIntent.create(params);

            if ("succeeded".equals(intent.getStatus())) {
                return PaymentGatewayResult.success(intent.getId());
            } else {
                return PaymentGatewayResult.failed(intent.getLastPaymentError().getMessage());
            }
        } catch (StripeException e) {
            return PaymentGatewayResult.failed(e.getMessage());
        }
    }
}
```

**REST API Adapter**:
```java
@RestController
@RequestMapping("/api/payments")
public class PaymentController {

    private final PaymentProcessor paymentProcessor;

    @PostMapping
    public ResponseEntity<PaymentResponse> processPayment(@RequestBody PaymentRequest request) {
        ProcessPaymentCommand command = PaymentRequestMapper.toCommand(request);
        PaymentResult result = paymentProcessor.processPayment(command);

        if (result.isSuccessful()) {
            return ResponseEntity.ok(PaymentResponse.success(result));
        } else {
            return ResponseEntity.badRequest().body(PaymentResponse.failed(result));
        }
    }
}
```

## Benefits Achieved

### Testability
- **Unit Testing**: Core logic tested without external dependencies
- **Integration Testing**: Easy to test with different adapter implementations
- **Mock Testing**: Ports can be easily mocked for testing

### Flexibility
- **Technology Independence**: Can switch databases, payment gateways, or messaging systems
- **Multiple Interfaces**: Same core logic exposed via REST, GraphQL, or messaging
- **Environment Adaptation**: Different adapters for different environments

### Maintainability
- **Clear Separation**: Business logic separated from technical concerns
- **Single Responsibility**: Each adapter has one responsibility
- **Dependency Direction**: Dependencies point inward toward the domain

## Best Practices Identified

1. **Keep Core Pure**: No external dependencies in domain layer
2. **Define Clear Ports**: Interfaces should be domain-focused, not technology-focused
3. **Thin Adapters**: Adapters should only translate, not contain business logic
4. **Test at Boundaries**: Focus testing on port implementations
5. **Use Dependency Injection**: Wire adapters to ports externally

## Conclusion

Hexagonal Architecture enabled PayFlow to create a flexible, testable payment processing system. The clear separation between business logic and external concerns made the system easier to maintain and adapt to changing requirements.