# Event-Driven Architecture Case Study

## Overview

Event-Driven Architecture enables components to communicate through events, with producers emitting events and consumers reacting to them asynchronously. This case study examines implementation at a logistics company managing supply chain operations.

## Business Context

**Company**: LogiFlow - A global logistics and supply chain management company
**Challenge**: Real-time coordination across warehouses, transportation, and customer systems
**Goal**: Create responsive system that reacts to supply chain events in real-time

## Architecture Implementation

### Event-Driven Components
- **Inventory Service**: Tracks stock levels and movements
- **Order Service**: Manages customer orders and fulfillment
- **Shipping Service**: Coordinates transportation and delivery
- **Notification Service**: Communicates with customers and partners
- **Analytics Service**: Processes events for business intelligence
- **Audit Service**: Maintains compliance and audit trails

### Event Implementation
```java
// Domain Events
public class InventoryLevelChangedEvent {
    private final String warehouseId;
    private final String productId;
    private final int previousLevel;
    private final int currentLevel;
    private final String reason;
    private final Instant timestamp;

    // Constructor and getters
}

public class OrderShippedEvent {
    private final String orderId;
    private final String customerId;
    private final String trackingNumber;
    private final String carrierId;
    private final Instant shippedAt;
    private final List<OrderItem> items;

    // Constructor and getters
}

// Event Publisher
@Component
public class EventPublisher {

    private final KafkaTemplate<String, Object> kafkaTemplate;

    public void publishEvent(DomainEvent event) {
        String topic = getTopicForEvent(event);
        String key = getPartitionKey(event);

        kafkaTemplate.send(topic, key, event)
            .addCallback(
                result -> log.info("Event published successfully: {}", event),
                failure -> log.error("Failed to publish event: {}", event, failure)
            );
    }

    private String getTopicForEvent(DomainEvent event) {
        return "logistics." + event.getClass().getSimpleName().toLowerCase();
    }

    private String getPartitionKey(DomainEvent event) {
        // Partition by warehouse or customer for ordered processing
        if (event instanceof InventoryLevelChangedEvent) {
            return ((InventoryLevelChangedEvent) event).getWarehouseId();
        } else if (event instanceof OrderShippedEvent) {
            return ((OrderShippedEvent) event).getCustomerId();
        }
        return event.getEventId();
    }
}

// Event Consumers
@Component
public class InventoryEventHandler {

    @KafkaListener(topics = "logistics.inventorylevelchangedevent")
    public void handleInventoryLevelChanged(InventoryLevelChangedEvent event) {
        // Check for low stock alerts
        if (event.getCurrentLevel() < getReorderThreshold(event.getProductId())) {
            publishReorderAlert(event);
        }

        // Update analytics
        analyticsService.recordInventoryChange(event);

        // Check pending orders that can now be fulfilled
        checkPendingOrders(event.getWarehouseId(), event.getProductId());
    }

    @KafkaListener(topics = "logistics.ordershippedevent")
    public void handleOrderShipped(OrderShippedEvent event) {
        // Send customer notification
        notificationService.sendShippingNotification(
            event.getCustomerId(),
            event.getTrackingNumber()
        );

        // Update order status
        orderService.updateOrderStatus(event.getOrderId(), OrderStatus.SHIPPED);

        // Record shipping metrics
        analyticsService.recordShipment(event);

        // Update inventory for shipped items
        for (OrderItem item : event.getItems()) {
            inventoryService.recordShipment(
                event.getWarehouseId(),
                item.getProductId(),
                item.getQuantity()
            );
        }
    }
}

// Saga Pattern for Complex Workflows
@Component
public class OrderFulfillmentSaga {

    @SagaOrchestrationStart
    @KafkaListener(topics = "logistics.ordercreatedevent")
    public void handleOrderCreated(OrderCreatedEvent event) {
        SagaTransaction saga = SagaTransaction.builder()
            .sagaId(event.getOrderId())
            .addStep(new ReserveInventoryStep(event))
            .addStep(new ProcessPaymentStep(event))
            .addStep(new AllocateWarehouseStep(event))
            .addStep(new CreateShipmentStep(event))
            .build();

        sagaManager.startSaga(saga);
    }

    @SagaStep("ReserveInventory")
    public void reserveInventory(ReserveInventoryStep step) {
        try {
            inventoryService.reserveItems(step.getOrderId(), step.getItems());
            sagaManager.completeStep(step.getSagaId(), step.getStepId());
        } catch (InsufficientInventoryException e) {
            sagaManager.failStep(step.getSagaId(), step.getStepId(), e.getMessage());
        }
    }

    @SagaCompensation("ReserveInventory")
    public void compensateInventoryReservation(ReserveInventoryStep step) {
        inventoryService.releaseReservation(step.getOrderId());
    }
}
```

## Results and Benefits

### Real-time Responsiveness
- **Event Processing**: Average event processing time under 100ms
- **System Responsiveness**: 95% of events processed within 1 second
- **Scalability**: Can handle 100K+ events per second
- **Availability**: 99.9% uptime with graceful degradation

### Business Benefits
- **Inventory Optimization**: 30% reduction in stockouts through real-time alerts
- **Customer Satisfaction**: Real-time order tracking and notifications
- **Operational Efficiency**: Automated workflows reduce manual intervention
- **Compliance**: Complete audit trail for regulatory requirements

### Technical Benefits
- **Loose Coupling**: Services can evolve independently
- **Scalability**: Individual services scale based on event load
- **Resilience**: System continues functioning during partial failures
- **Extensibility**: Easy to add new event consumers

## Challenges and Solutions

### Event Ordering
**Problem**: Some business processes require events to be processed in order.
**Solution**: Used Kafka partitioning with consistent partition keys to ensure ordering within partitions.

### Duplicate Events
**Problem**: Network issues can cause duplicate event delivery.
**Solution**: Implemented idempotent event handlers using event IDs and database constraints.

### Event Schema Evolution
**Problem**: Changing event schemas can break existing consumers.
**Solution**: Used schema registry with backward compatibility rules and event versioning.

## Best Practices Identified

1. **Design Events as Facts**: Events should represent what happened, not commands
2. **Ensure Idempotency**: Event handlers should be safe to retry
3. **Use Correlation IDs**: Track related events across the system
4. **Plan for Schema Evolution**: Version events and maintain backward compatibility
5. **Monitor Event Flows**: Track event processing metrics and failures
6. **Implement Dead Letter Queues**: Handle events that can't be processed

## Conclusion

Event-Driven Architecture enabled LogiFlow to create a responsive, scalable logistics platform. The key was designing events as immutable facts and ensuring all event handlers were idempotent and resilient to failures.