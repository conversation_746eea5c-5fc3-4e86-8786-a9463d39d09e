# Strangler Fig Pattern: Legacy Monolith to Microservices Migration

## Problem Statement

**FinTech Global**, a financial services company, has been operating with a monolithic Java application for over 10 years. The application handles customer accounts, transactions, reporting, and regulatory compliance. The company faces several challenges:

1. The monolith has become difficult to maintain and extend
2. Deployment cycles are slow (monthly releases)
3. Technology stack is outdated (Java 8, Spring 4.x)
4. Scaling is inefficient as the entire application must be scaled
5. New feature development is slow due to complex dependencies
6. The team wants to adopt modern technologies and practices

## Solution: Strangler Fig Pattern with AWS and Kubernetes

The team decides to implement the Strangler Fig Pattern to gradually migrate the monolith to a microservices architecture while keeping the business running.

### Architecture Overview

![Strangler Fig Pattern Architecture](../images/strangler-fig-pattern.png)

### Key Components

1. **API Gateway Layer**
   - **AWS API Gateway**: Acts as the facade in front of both the legacy monolith and new microservices
   - **Amazon CloudFront**: Provides CDN capabilities for static content
   - **AWS WAF**: Protects both legacy and new components

2. **Legacy Monolith**
   - **Amazon EC2**: Hosts the existing Java monolith
   - **Amazon RDS (Oracle)**: Maintains the existing relational database

3. **New Microservices**
   - **Amazon EKS (Kubernetes)**: Orchestrates containerized microservices
   - **AWS Lambda**: Hosts serverless functions for event-driven components
   - **Amazon DynamoDB**: Provides NoSQL database for appropriate services
   - **Amazon Aurora**: Offers relational database capabilities for services requiring ACID properties

4. **Shared Services**
   - **Amazon Cognito**: Manages authentication across both old and new systems
   - **AWS EventBridge**: Facilitates event-driven communication
   - **Amazon SQS/SNS**: Enables asynchronous messaging

### Implementation Strategy

#### Phase 1: Preparation and API Gateway Setup

1. **API Analysis and Documentation**
   - Document all existing APIs in the monolith
   - Identify API usage patterns and dependencies
   - Prioritize APIs for migration based on business value and complexity

2. **API Gateway Implementation**
   - Deploy AWS API Gateway as the facade
   - Configure routes to direct traffic to the monolith
   - Implement monitoring and logging for all API calls

3. **Authentication Modernization**
   - Implement Amazon Cognito for identity management
   - Create an authentication service that works with both systems
   - Update the monolith to accept the new authentication tokens

#### Phase 2: First Microservice Migration - Customer Profile

1. **Service Implementation**
   - Develop a new Customer Profile microservice using Spring Boot
   - Deploy to Amazon EKS using Kubernetes manifests
   - Implement a DynamoDB database for customer data

2. **Data Migration Strategy**
   - Create a data synchronization mechanism using AWS DMS
   - Implement dual-write pattern during transition
   - Validate data consistency between old and new systems

3. **Traffic Routing**
   - Update API Gateway to route customer profile requests to the new service
   - Implement feature flags to control routing percentages
   - Monitor performance and errors closely

#### Phase 3: Transaction Service Migration

1. **Service Implementation**
   - Develop Transaction microservice with Kotlin and Spring Boot
   - Deploy to EKS with horizontal pod autoscaling
   - Use Amazon Aurora for transaction data

2. **Event-Driven Integration**
   - Implement AWS EventBridge for transaction events
   - Create event consumers in both legacy and new systems
   - Ensure transaction consistency across systems

3. **Gradual Traffic Shift**
   - Route increasing percentage of transaction requests to new service
   - Implement canary deployments using Kubernetes
   - Monitor transaction success rates and performance

#### Phase 4: Reporting and Analytics Migration

1. **Service Implementation**
   - Develop reporting microservices using AWS Lambda
   - Implement data lake using Amazon S3 and Athena
   - Create real-time dashboards with Amazon QuickSight

2. **Data Synchronization**
   - Use AWS Glue for ETL processes
   - Implement change data capture from legacy database
   - Validate reporting accuracy between systems

#### Phase 5: Complete Migration and Legacy Retirement

1. **Final Services Migration**
   - Complete migration of remaining functionality
   - Ensure all data is migrated and validated
   - Perform comprehensive testing of the new architecture

2. **Legacy System Retirement**
   - Gradually reduce resources allocated to legacy system
   - Archive historical data
   - Decommission legacy infrastructure

## Implementation Details

### Kubernetes Resources for Customer Profile Service

```yaml
# customer-profile-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: customer-profile
  namespace: financial-services
spec:
  replicas: 3
  selector:
    matchLabels:
      app: customer-profile
  template:
    metadata:
      labels:
        app: customer-profile
    spec:
      containers:
      - name: customer-profile
        image: fintech-global/customer-profile:1.0.0
        ports:
        - containerPort: 8080
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "production"
        - name: DYNAMODB_TABLE_NAME
          valueFrom:
            configMapKeyRef:
              name: customer-profile-config
              key: dynamodb.table.name
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 15
```

### AWS API Gateway Configuration

```json
{
  "swagger": "2.0",
  "info": {
    "title": "FinTech Global API",
    "version": "1.0.0"
  },
  "paths": {
    "/customers/{customerId}": {
      "get": {
        "produces": [
          "application/json"
        ],
        "parameters": [
          {
            "name": "customerId",
            "in": "path",
            "required": true,
            "type": "string"
          }
        ],
        "responses": {
          "200": {
            "description": "200 response"
          }
        },
        "x-amazon-apigateway-integration": {
          "uri": "http://customer-profile.financial-services.svc.cluster.local/customers/{customerId}",
          "responses": {
            "default": {
              "statusCode": "200"
            }
          },
          "requestParameters": {
            "integration.request.path.customerId": "method.request.path.customerId"
          },
          "passthroughBehavior": "when_no_match",
          "httpMethod": "GET",
          "type": "http_proxy"
        }
      }
    },
    "/transactions": {
      "post": {
        "produces": [
          "application/json"
        ],
        "responses": {
          "200": {
            "description": "200 response"
          }
        },
        "x-amazon-apigateway-integration": {
          "uri": "http://legacy-monolith.financial-services.svc.cluster.local/api/transactions",
          "responses": {
            "default": {
              "statusCode": "200"
            }
          },
          "passthroughBehavior": "when_no_match",
          "httpMethod": "POST",
          "type": "http_proxy"
        }
      }
    }
  }
}
```

### Data Synchronization Lambda Function

```javascript
// customer-sync-lambda.js
const AWS = require('aws-sdk');
const dynamoDB = new AWS.DynamoDB.DocumentClient();
const sqs = new AWS.SQS();

exports.handler = async (event) => {
    try {
        // Process customer data change events from SQS
        for (const record of event.Records) {
            const customerData = JSON.parse(record.body);
            
            // Update DynamoDB with new customer data
            await dynamoDB.put({
                TableName: process.env.CUSTOMER_TABLE,
                Item: {
                    customerId: customerData.id,
                    firstName: customerData.firstName,
                    lastName: customerData.lastName,
                    email: customerData.email,
                    updatedAt: new Date().toISOString()
                }
            }).promise();
            
            console.log(`Successfully synchronized customer ${customerData.id}`);
        }
        
        return { statusCode: 200, body: 'Synchronization complete' };
    } catch (error) {
        console.error('Error synchronizing customer data:', error);
        throw error;
    }
};
```

## Benefits and Considerations

### Benefits

1. **Business Continuity**: The business continues to operate during the migration
2. **Incremental Modernization**: Each component is modernized independently
3. **Risk Reduction**: Smaller, incremental changes reduce the risk of major failures
4. **Early ROI**: Benefits are realized as each microservice is deployed
5. **Technology Flexibility**: Different services can use different technologies
6. **Improved Scalability**: New services can scale independently based on demand
7. **Enhanced Developer Experience**: Teams can work on smaller, focused components

### Considerations and Challenges

1. **Data Consistency**: Maintaining data consistency between old and new systems
2. **Complex Routing Logic**: API Gateway routing rules become more complex during migration
3. **Testing Overhead**: Need for comprehensive testing of both systems
4. **Dual Maintenance**: Both systems require maintenance during the transition
5. **Team Skills**: Requires team members skilled in both legacy and modern technologies
6. **Cost Management**: Running parallel systems temporarily increases costs

## Conclusion

The Strangler Fig Pattern provided FinTech Global with a pragmatic approach to modernize their legacy monolith without disrupting business operations. By leveraging AWS services and Kubernetes, they were able to gradually migrate to a microservices architecture while reducing risk and delivering value incrementally.

The migration took 18 months to complete, with the first microservice deployed after just 3 months. This approach allowed the company to start benefiting from the new architecture early while methodically replacing the legacy system.

## References

1. AWS Prescriptive Guidance: [Strangler Fig Pattern](https://docs.aws.amazon.com/prescriptive-guidance/latest/modernization-decomposing-monoliths/strangler-fig-pattern.html)
2. Martin Fowler: [Strangler Fig Application](https://martinfowler.com/bliki/StranglerFigApplication.html)
3. Kubernetes Documentation: [Deployments](https://kubernetes.io/docs/concepts/workloads/controllers/deployment/)
4. AWS API Gateway: [HTTP API Reference](https://docs.aws.amazon.com/apigateway/latest/developerguide/http-api-develop.html)
