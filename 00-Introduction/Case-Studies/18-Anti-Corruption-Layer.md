# Anti-Corruption Layer Case Study

## Overview

An Anti-Corruption Layer preserves the integrity of a domain model by translating between different models or systems. This case study examines implementation during a legacy system modernization at a healthcare provider.

## Business Context

**Company**: HealthTech Solutions - A healthcare management system provider
**Challenge**: Modernizing patient management system while maintaining integration with legacy billing system
**Goal**: Protect new domain model from legacy system's poor design while maintaining data flow

## Implementation Strategy

### Anti-Corruption Layer Design
```java
// Modern Domain Model
public class Patient {
    private PatientId id;
    private PersonalInfo personalInfo;
    private ContactInfo contactInfo;
    private InsuranceInfo insuranceInfo;
    private List<MedicalRecord> medicalRecords;

    // Clean domain methods
    public void updateContactInfo(ContactInfo newContactInfo) {
        this.contactInfo = newContactInfo;
        DomainEventPublisher.publish(new PatientContactUpdatedEvent(this.id));
    }

    public void addMedicalRecord(MedicalRecord record) {
        this.medicalRecords.add(record);
        DomainEventPublisher.publish(new MedicalRecordAddedEvent(this.id, record.getId()));
    }
}

// Anti-Corruption Layer Interface
public interface LegacyBillingSystemAdapter {
    void syncPatientToBilling(Patient patient);
    void syncBillingToPatient(LegacyPatientData legacyData);
    BillingStatus getBillingStatus(PatientId patientId);
}

// Anti-Corruption Layer Implementation
@Component
public class LegacyBillingSystemAdapterImpl implements LegacyBillingSystemAdapter {

    private final LegacyBillingSystemClient legacyClient;
    private final PatientTranslator translator;

    @Override
    public void syncPatientToBilling(Patient patient) {
        // Translate modern domain model to legacy format
        LegacyPatientRecord legacyRecord = translator.translateToLegacy(patient);

        try {
            // Call legacy system
            legacyClient.updatePatientRecord(legacyRecord);
        } catch (LegacySystemException e) {
            // Handle legacy system quirks
            handleLegacySystemError(e, patient);
        }
    }

    @Override
    public void syncBillingToPatient(LegacyPatientData legacyData) {
        // Translate legacy data to modern domain model
        Patient patient = translator.translateFromLegacy(legacyData);

        // Apply business rules that legacy system doesn't enforce
        validateAndEnrichPatientData(patient);

        patientRepository.save(patient);
    }

    private void handleLegacySystemError(LegacySystemException e, Patient patient) {
        // Legacy system has specific error codes and behaviors
        switch (e.getErrorCode()) {
            case "PATIENT_NOT_FOUND":
                // Create new patient in legacy system
                createPatientInLegacySystem(patient);
                break;
            case "INVALID_INSURANCE_FORMAT":
                // Legacy system expects specific insurance format
                retryWithFormattedInsurance(patient);
                break;
            default:
                throw new PatientSyncException("Failed to sync patient to billing system", e);
        }
    }
}

// Translation Logic
@Component
public class PatientTranslator {

    public LegacyPatientRecord translateToLegacy(Patient patient) {
        LegacyPatientRecord legacy = new LegacyPatientRecord();

        // Handle naming differences
        legacy.setPatientNumber(patient.getId().getValue());
        legacy.setFirstName(patient.getPersonalInfo().getFirstName());
        legacy.setLastName(patient.getPersonalInfo().getLastName());

        // Handle data format differences
        legacy.setBirthDate(formatDateForLegacy(patient.getPersonalInfo().getBirthDate()));
        legacy.setPhoneNumber(formatPhoneForLegacy(patient.getContactInfo().getPhoneNumber()));

        // Handle business rule differences
        legacy.setInsuranceCode(mapInsuranceToLegacyCode(patient.getInsuranceInfo()));

        // Handle legacy system limitations
        if (patient.getPersonalInfo().getMiddleName() != null) {
            // Legacy system doesn't support middle names, concatenate with first name
            legacy.setFirstName(patient.getPersonalInfo().getFirstName() + " " +
                               patient.getPersonalInfo().getMiddleName());
        }

        return legacy;
    }

    public Patient translateFromLegacy(LegacyPatientData legacyData) {
        // Extract data from legacy format
        PatientId id = new PatientId(legacyData.getPatientNumber());

        PersonalInfo personalInfo = PersonalInfo.builder()
            .firstName(extractFirstName(legacyData.getFirstName()))
            .middleName(extractMiddleName(legacyData.getFirstName()))
            .lastName(legacyData.getLastName())
            .birthDate(parseLegacyDate(legacyData.getBirthDate()))
            .build();

        ContactInfo contactInfo = ContactInfo.builder()
            .phoneNumber(parseLegacyPhone(legacyData.getPhoneNumber()))
            .address(translateLegacyAddress(legacyData.getAddress()))
            .build();

        InsuranceInfo insuranceInfo = mapLegacyInsuranceCode(legacyData.getInsuranceCode());

        return Patient.builder()
            .id(id)
            .personalInfo(personalInfo)
            .contactInfo(contactInfo)
            .insuranceInfo(insuranceInfo)
            .build();
    }

    private String formatDateForLegacy(LocalDate date) {
        // Legacy system expects MM/DD/YYYY format
        return date.format(DateTimeFormatter.ofPattern("MM/dd/yyyy"));
    }

    private String formatPhoneForLegacy(PhoneNumber phone) {
        // Legacy system expects (XXX) XXX-XXXX format
        return String.format("(%s) %s-%s",
            phone.getAreaCode(),
            phone.getExchange(),
            phone.getNumber());
    }

    private String mapInsuranceToLegacyCode(InsuranceInfo insurance) {
        // Legacy system uses different insurance codes
        Map<String, String> insuranceMapping = Map.of(
            "BLUE_CROSS", "BC",
            "AETNA", "AET",
            "CIGNA", "CIG",
            "MEDICARE", "MED"
        );

        return insuranceMapping.getOrDefault(
            insurance.getProvider().getCode(),
            "UNK"
        );
    }
}
```

## Results and Benefits

### Domain Model Protection
- **Clean Architecture**: Modern system maintains clean domain model
- **Legacy Isolation**: Legacy system quirks contained in ACL
- **Independent Evolution**: Both systems can evolve independently
- **Business Rule Consistency**: Modern business rules enforced regardless of legacy data

### Integration Benefits
- **Data Consistency**: Reliable translation between systems
- **Error Handling**: Graceful handling of legacy system limitations
- **Performance**: Optimized integration patterns reduce latency
- **Monitoring**: Clear visibility into translation and sync processes

## Best Practices Identified

1. **Comprehensive Translation**: Handle all data format and business rule differences
2. **Error Resilience**: Plan for legacy system quirks and failures
3. **Bidirectional Sync**: Support data flow in both directions
4. **Validation**: Validate translated data before using in domain
5. **Monitoring**: Track translation success rates and performance
6. **Documentation**: Document all translation rules and mappings

## Conclusion

The Anti-Corruption Layer successfully enabled HealthTech Solutions to modernize their patient management system while maintaining integration with the legacy billing system. The ACL protected the new domain model from legacy system contamination while ensuring reliable data flow between systems.