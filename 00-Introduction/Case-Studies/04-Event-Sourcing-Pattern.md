# Event Sourcing Pattern: Supply Chain Tracking System

## Problem Statement

**LogiChain**, a global logistics company, needs to build a comprehensive supply chain tracking system with the following requirements:

1. Complete audit trail of all shipments and inventory movements
2. Ability to reconstruct the state of any shipment at any point in time
3. Support for complex analytics and reporting across the supply chain
4. Integration with multiple partners, carriers, and customs systems
5. Regulatory compliance with various international shipping regulations
6. High throughput to handle millions of daily tracking events
7. Resilience against data loss or corruption

Their current system stores only the current state of shipments, making it impossible to trace the full history of changes or understand exactly what happened when issues arise.

## Solution: Event Sourcing Pattern with AWS and Kubernetes

The team implements the Event Sourcing pattern, storing all changes to the application state as a sequence of events rather than just the current state. This approach provides a complete history that can be used to reconstruct past states and analyze the evolution of shipments through the supply chain.

### Architecture Overview

![Event Sourcing Pattern Architecture](../images/event-sourcing-pattern.png)

### Key Components

1. **Event Capture and Storage**
   - **Amazon Kinesis Data Streams**: For real-time event ingestion
   - **Amazon S3**: For long-term event storage (event store)
   - **Amazon DynamoDB**: For event metadata and indexing
   - **AWS Lambda**: For event processing and validation

2. **State Projection and Querying**
   - **Amazon ElastiCache (Redis)**: For current state materialized views
   - **Amazon Aurora PostgreSQL**: For relational views of shipment data
   - **Amazon OpenSearch**: For complex queries and full-text search
   - **Amazon EKS**: For running projection and query services

3. **Analytics and Reporting**
   - **Amazon Redshift**: For data warehousing and analytics
   - **Amazon QuickSight**: For visualization and dashboards
   - **Amazon EMR**: For big data processing of historical events

4. **Integration and APIs**
   - **Amazon API Gateway**: For external API access
   - **AWS AppSync**: For GraphQL APIs and real-time updates
   - **Amazon EventBridge**: For integration with partner systems

### Implementation Strategy

#### Phase 1: Event Capture Infrastructure

1. **Event Schema Design**
   - Define comprehensive event schemas for all supply chain events
   - Implement versioning strategy for event evolution
   - Create validation rules for event integrity
   - Document event relationships and dependencies

2. **Event Ingestion Pipeline**
   - Deploy Kinesis Data Streams for real-time event capture
   - Implement AWS Lambda functions for event validation and enrichment
   - Create S3 buckets with appropriate lifecycle policies for event storage
   - Set up DynamoDB tables for event metadata and indexing

3. **Event Producer Integration**
   - Develop SDKs for internal systems to produce events
   - Create API endpoints for partner systems to submit events
   - Implement mobile applications for field personnel to record events
   - Set up IoT integration for automated event generation from sensors

#### Phase 2: State Projection Services

1. **Materialized View Development**
   - Design materialized view schemas for different query patterns
   - Implement projection services to process events and update views
   - Deploy services to Amazon EKS with appropriate scaling
   - Set up caching strategies for frequently accessed data

2. **Query API Development**
   - Create REST APIs for standard queries
   - Implement GraphQL API for flexible querying
   - Develop specialized endpoints for mobile applications
   - Set up WebSocket connections for real-time updates

#### Phase 3: Analytics and Reporting

1. **Data Warehouse Integration**
   - Set up ETL pipelines to load events into Redshift
   - Create analytical schemas optimized for reporting
   - Implement scheduled jobs for regular data processing
   - Develop custom metrics and KPIs for supply chain performance

2. **Dashboard Development**
   - Create executive dashboards for overall performance
   - Develop operational dashboards for day-to-day monitoring
   - Implement customer-facing tracking portals
   - Set up automated reporting and alerting

#### Phase 4: Temporal Querying and Replay

1. **Temporal Query Service**
   - Develop services to reconstruct past states from event streams
   - Implement time-based querying capabilities
   - Create comparison tools to analyze changes over time
   - Set up snapshot generation for performance optimization

2. **Event Replay Capabilities**
   - Implement event replay functionality for system recovery
   - Create tools for testing and simulation using historical data
   - Develop debugging capabilities using event replay
   - Set up sandbox environments for "what-if" analysis

## Implementation Details

### Event Schema Example

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "title": "ShipmentCreatedEvent",
  "type": "object",
  "required": [
    "eventId",
    "eventType",
    "eventTime",
    "shipmentId",
    "origin",
    "destination",
    "items",
    "expectedDeliveryDate"
  ],
  "properties": {
    "eventId": {
      "type": "string",
      "format": "uuid",
      "description": "Unique identifier for this event"
    },
    "eventType": {
      "type": "string",
      "enum": ["ShipmentCreated"],
      "description": "Type of the event"
    },
    "eventTime": {
      "type": "string",
      "format": "date-time",
      "description": "ISO8601 timestamp when the event occurred"
    },
    "shipmentId": {
      "type": "string",
      "description": "Unique identifier for the shipment"
    },
    "version": {
      "type": "integer",
      "default": 1,
      "description": "Version of the event schema"
    },
    "origin": {
      "type": "object",
      "required": ["locationId", "address"],
      "properties": {
        "locationId": {
          "type": "string",
          "description": "Identifier for the origin location"
        },
        "address": {
          "type": "object",
          "required": ["country", "postalCode"],
          "properties": {
            "street": { "type": "string" },
            "city": { "type": "string" },
            "state": { "type": "string" },
            "country": { "type": "string" },
            "postalCode": { "type": "string" }
          }
        },
        "coordinates": {
          "type": "object",
          "properties": {
            "latitude": { "type": "number" },
            "longitude": { "type": "number" }
          }
        }
      }
    },
    "destination": {
      "type": "object",
      "required": ["locationId", "address"],
      "properties": {
        "locationId": {
          "type": "string",
          "description": "Identifier for the destination location"
        },
        "address": {
          "type": "object",
          "required": ["country", "postalCode"],
          "properties": {
            "street": { "type": "string" },
            "city": { "type": "string" },
            "state": { "type": "string" },
            "country": { "type": "string" },
            "postalCode": { "type": "string" }
          }
        },
        "coordinates": {
          "type": "object",
          "properties": {
            "latitude": { "type": "number" },
            "longitude": { "type": "number" }
          }
        }
      }
    },
    "items": {
      "type": "array",
      "minItems": 1,
      "items": {
        "type": "object",
        "required": ["itemId", "description", "quantity", "weight"],
        "properties": {
          "itemId": { "type": "string" },
          "description": { "type": "string" },
          "quantity": { "type": "integer", "minimum": 1 },
          "weight": {
            "type": "object",
            "required": ["value", "unit"],
            "properties": {
              "value": { "type": "number", "minimum": 0 },
              "unit": { "type": "string", "enum": ["kg", "lb"] }
            }
          },
          "dimensions": {
            "type": "object",
            "properties": {
              "length": { "type": "number", "minimum": 0 },
              "width": { "type": "number", "minimum": 0 },
              "height": { "type": "number", "minimum": 0 },
              "unit": { "type": "string", "enum": ["cm", "in"] }
            },
            "required": ["length", "width", "height", "unit"]
          }
        }
      }
    },
    "expectedDeliveryDate": {
      "type": "string",
      "format": "date-time"
    },
    "serviceLevel": {
      "type": "string",
      "enum": ["Standard", "Express", "Priority", "Economy"]
    },
    "customerId": {
      "type": "string"
    },
    "carrierInformation": {
      "type": "object",
      "properties": {
        "carrierId": { "type": "string" },
        "serviceName": { "type": "string" },
        "accountNumber": { "type": "string" }
      }
    },
    "customsInformation": {
      "type": "object",
      "properties": {
        "contentType": { "type": "string", "enum": ["Documents", "Merchandise", "Gift", "Sample", "ReturnedGoods"] },
        "customsValue": {
          "type": "object",
          "properties": {
            "amount": { "type": "number", "minimum": 0 },
            "currency": { "type": "string" }
          },
          "required": ["amount", "currency"]
        },
        "dutiable": { "type": "boolean" }
      }
    },
    "metadata": {
      "type": "object",
      "additionalProperties": true
    }
  }
}
```

### Event Processing Lambda Function

```javascript
// shipment-event-processor.js
const AWS = require('aws-sdk');
const { v4: uuidv4 } = require('uuid');
const s3 = new AWS.S3();
const dynamoDB = new AWS.DynamoDB.DocumentClient();
const redis = new AWS.ElastiCache();

exports.handler = async (event) => {
    try {
        // Process events from Kinesis
        const records = event.Records.map(record => {
            // Decode and parse the Kinesis record
            const payload = Buffer.from(record.kinesis.data, 'base64').toString('utf-8');
            const shipmentEvent = JSON.parse(payload);
            
            // Validate event structure
            validateEvent(shipmentEvent);
            
            return shipmentEvent;
        });
        
        // Store events in S3 (event store)
        await storeEventsInS3(records);
        
        // Update event metadata in DynamoDB
        await updateEventMetadata(records);
        
        // Update materialized view in Redis
        await updateMaterializedView(records);
        
        return { statusCode: 200, body: `Processed ${records.length} events` };
    } catch (error) {
        console.error('Error processing shipment events:', error);
        throw error;
    }
};

function validateEvent(event) {
    // Basic validation
    if (!event.eventId || !event.eventType || !event.eventTime || !event.shipmentId) {
        throw new Error(`Invalid event structure: ${JSON.stringify(event)}`);
    }
    
    // Event-specific validation
    switch (event.eventType) {
        case 'ShipmentCreated':
            if (!event.origin || !event.destination || !event.items || !event.expectedDeliveryDate) {
                throw new Error(`Invalid ShipmentCreated event: ${JSON.stringify(event)}`);
            }
            break;
        case 'ShipmentPickedUp':
            if (!event.location || !event.timestamp || !event.handledBy) {
                throw new Error(`Invalid ShipmentPickedUp event: ${JSON.stringify(event)}`);
            }
            break;
        // Add validation for other event types
    }
}

async function storeEventsInS3(events) {
    const date = new Date();
    const year = date.getUTCFullYear();
    const month = String(date.getUTCMonth() + 1).padStart(2, '0');
    const day = String(date.getUTCDate()).padStart(2, '0');
    
    // Group events by shipment ID
    const shipmentEvents = {};
    events.forEach(event => {
        if (!shipmentEvents[event.shipmentId]) {
            shipmentEvents[event.shipmentId] = [];
        }
        shipmentEvents[event.shipmentId].push(event);
    });
    
    // Store events in S3 by shipment ID
    const storePromises = Object.entries(shipmentEvents).map(async ([shipmentId, shipmentEvents]) => {
        const key = `events/${year}/${month}/${day}/${shipmentId}/${uuidv4()}.json`;
        
        await s3.putObject({
            Bucket: process.env.EVENT_STORE_BUCKET,
            Key: key,
            Body: JSON.stringify(shipmentEvents),
            ContentType: 'application/json'
        }).promise();
        
        return key;
    });
    
    return Promise.all(storePromises);
}

async function updateEventMetadata(events) {
    const batchWriteRequests = [];
    
    events.forEach(event => {
        batchWriteRequests.push({
            PutRequest: {
                Item: {
                    eventId: event.eventId,
                    shipmentId: event.shipmentId,
                    eventType: event.eventType,
                    eventTime: event.eventTime,
                    version: event.version || 1,
                    processed: new Date().toISOString()
                }
            }
        });
    });
    
    // DynamoDB can only process 25 items in a batch
    for (let i = 0; i < batchWriteRequests.length; i += 25) {
        const batch = batchWriteRequests.slice(i, i + 25);
        
        await dynamoDB.batchWrite({
            RequestItems: {
                [process.env.EVENT_METADATA_TABLE]: batch
            }
        }).promise();
    }
}

async function updateMaterializedView(events) {
    // Group events by shipment ID to process them together
    const shipmentEvents = {};
    events.forEach(event => {
        if (!shipmentEvents[event.shipmentId]) {
            shipmentEvents[event.shipmentId] = [];
        }
        shipmentEvents[event.shipmentId].push(event);
    });
    
    // Process each shipment's events in order
    for (const [shipmentId, events] of Object.entries(shipmentEvents)) {
        // Sort events by time to ensure correct order
        events.sort((a, b) => new Date(a.eventTime) - new Date(b.eventTime));
        
        // Get current state from Redis
        const shipmentKey = `shipment:${shipmentId}`;
        let shipment = await getFromRedis(shipmentKey) || { shipmentId };
        
        // Apply each event to update the state
        for (const event of events) {
            shipment = applyEvent(shipment, event);
        }
        
        // Store updated state in Redis
        await setInRedis(shipmentKey, shipment);
    }
}

function applyEvent(shipment, event) {
    switch (event.eventType) {
        case 'ShipmentCreated':
            return {
                ...shipment,
                origin: event.origin,
                destination: event.destination,
                items: event.items,
                expectedDeliveryDate: event.expectedDeliveryDate,
                serviceLevel: event.serviceLevel,
                customerId: event.customerId,
                carrierInformation: event.carrierInformation,
                customsInformation: event.customsInformation,
                status: 'CREATED',
                createdAt: event.eventTime,
                lastUpdated: event.eventTime
            };
            
        case 'ShipmentPickedUp':
            return {
                ...shipment,
                status: 'IN_TRANSIT',
                currentLocation: event.location,
                pickupTime: event.timestamp,
                handledBy: event.handledBy,
                lastUpdated: event.eventTime
            };
            
        case 'ShipmentInTransit':
            return {
                ...shipment,
                status: 'IN_TRANSIT',
                currentLocation: event.location,
                lastScanTime: event.timestamp,
                transitEvents: [...(shipment.transitEvents || []), {
                    location: event.location,
                    timestamp: event.timestamp,
                    description: event.description
                }],
                lastUpdated: event.eventTime
            };
            
        case 'ShipmentDelivered':
            return {
                ...shipment,
                status: 'DELIVERED',
                currentLocation: event.location,
                deliveryTime: event.timestamp,
                receivedBy: event.receivedBy,
                proof: event.proof,
                lastUpdated: event.eventTime
            };
            
        case 'ShipmentException':
            return {
                ...shipment,
                status: 'EXCEPTION',
                exception: {
                    type: event.exceptionType,
                    description: event.description,
                    location: event.location,
                    timestamp: event.timestamp,
                    resolutionAction: event.resolutionAction
                },
                lastUpdated: event.eventTime
            };
            
        default:
            console.warn(`Unknown event type: ${event.eventType}`);
            return shipment;
    }
}

// Redis helper functions (simplified)
async function getFromRedis(key) {
    // In a real implementation, this would use the Redis client
    // This is a simplified placeholder
    console.log(`Getting ${key} from Redis`);
    return null; // Simulating no existing data
}

async function setInRedis(key, value) {
    // In a real implementation, this would use the Redis client
    // This is a simplified placeholder
    console.log(`Setting ${key} in Redis to:`, value);
    return true;
}
```

### Kubernetes Deployment for Projection Service

```yaml
# shipment-projection-service.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: shipment-projection-service
  namespace: logichain
spec:
  replicas: 3
  selector:
    matchLabels:
      app: shipment-projection-service
  template:
    metadata:
      labels:
        app: shipment-projection-service
    spec:
      containers:
      - name: shipment-projection-service
        image: logichain/shipment-projection-service:1.0.0
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "production"
        - name: AWS_REGION
          value: "us-west-2"
        - name: EVENT_STORE_BUCKET
          valueFrom:
            configMapKeyRef:
              name: event-sourcing-config
              key: event.store.bucket
        - name: EVENT_METADATA_TABLE
          valueFrom:
            configMapKeyRef:
              name: event-sourcing-config
              key: event.metadata.table
        - name: REDIS_HOST
          valueFrom:
            secretKeyRef:
              name: redis-credentials
              key: host
        - name: REDIS_PORT
          valueFrom:
            secretKeyRef:
              name: redis-credentials
              key: port
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-credentials
              key: password
        - name: POSTGRES_HOST
          valueFrom:
            secretKeyRef:
              name: postgres-credentials
              key: host
        - name: POSTGRES_PORT
          valueFrom:
            secretKeyRef:
              name: postgres-credentials
              key: port
        - name: POSTGRES_DATABASE
          valueFrom:
            secretKeyRef:
              name: postgres-credentials
              key: database
        - name: POSTGRES_USERNAME
          valueFrom:
            secretKeyRef:
              name: postgres-credentials
              key: username
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-credentials
              key: password
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: shipment-projection-service
  namespace: logichain
spec:
  selector:
    app: shipment-projection-service
  ports:
  - port: 80
    targetPort: 8080
  type: ClusterIP
```

## Benefits and Considerations

### Benefits

1. **Complete Audit Trail**: Every change is captured as an immutable event
2. **Temporal Querying**: Ability to reconstruct the state at any point in time
3. **Debugging Capabilities**: Events provide context for understanding issues
4. **Resilience**: Event log serves as the source of truth for recovery
5. **Scalability**: Event processing can be distributed and parallelized
6. **Flexibility**: New projections can be created without changing the event store
7. **Integration**: Events provide natural integration points for external systems

### Considerations and Challenges

1. **Eventual Consistency**: Projections may lag behind the event store
2. **Complexity**: More complex than traditional CRUD architectures
3. **Event Schema Evolution**: Managing changes to event schemas over time
4. **Storage Requirements**: Storing all events requires more storage
5. **Query Performance**: Complex queries may require specialized projections
6. **Learning Curve**: Team needs to understand event sourcing principles
7. **Operational Overhead**: Managing event store and projections adds complexity

## Conclusion

By implementing the Event Sourcing pattern, LogiChain successfully built a comprehensive supply chain tracking system that provides complete visibility into the lifecycle of shipments. The event-based architecture captures every change as an immutable event, enabling detailed auditing, temporal querying, and flexible projections for different use cases.

Key improvements included:
- Complete traceability of all shipment activities
- Ability to reconstruct the exact state of any shipment at any point in time
- Enhanced analytics capabilities for supply chain optimization
- Improved compliance with international shipping regulations
- Better resilience against data loss or corruption
- Flexibility to create new views and projections without changing the core system

The event-sourced architecture also provided a solid foundation for future enhancements, such as machine learning for predictive analytics, integration with blockchain for enhanced security, and real-time optimization of logistics operations.

## References

1. Martin Fowler: [Event Sourcing](https://martinfowler.com/eaaDev/EventSourcing.html)
2. AWS Architecture Blog: [Event Sourcing with AWS Lambda](https://aws.amazon.com/blogs/compute/event-sourcing-with-aws-lambda/)
3. Kubernetes Documentation: [StatefulSets](https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/)
4. AWS Kinesis: [Data Streams Developer Guide](https://docs.aws.amazon.com/streams/latest/dev/introduction.html)
