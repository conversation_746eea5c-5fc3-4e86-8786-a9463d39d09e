# Self-Service Infrastructure

**Skill Level: Intermediate**

## Notes

Self-service infrastructure empowers development teams to provision and manage their own infrastructure resources without requiring intervention from operations teams. This approach reduces bottlenecks, accelerates development cycles, and allows infrastructure teams to focus on building platforms rather than fulfilling tickets.

### Key Concepts

1. **Infrastructure Abstraction**: Hiding complex infrastructure details behind simple interfaces
2. **Service Catalogs**: Curated collections of pre-approved infrastructure components
3. **Infrastructure as Code (IaC) Templates**: Standardized, reusable infrastructure definitions
4. **Policy as Code**: Automated enforcement of organizational policies and guardrails
5. **Quota Management**: Allocation and control of resource consumption
6. **Cost Visibility**: Transparent tracking and attribution of infrastructure costs
7. **Approval Workflows**: Automated or human approval processes for resource requests
8. **Infrastructure Lifecycle Management**: Automated provisioning, updating, and decommissioning

### Implementation Approaches

1. **Custom Portals**: Internally developed web interfaces for infrastructure provisioning
2. **Cloud Service Catalogs**: Native cloud provider service catalogs (AWS Service Catalog, Azure Marketplace)
3. **Infrastructure Platforms**: Specialized tools like Terraform Cloud, Env0, or Spacelift
4. **Kubernetes Operators**: Custom operators that provision infrastructure based on custom resources
5. **ChatOps Interfaces**: Conversational interfaces for infrastructure provisioning via chat platforms
6. **CLI Tools**: Command-line interfaces with simplified abstractions

## Practical Example: Self-Service Infrastructure Platform

### 1. Service Catalog Definition

```yaml
# service-catalog.yaml
catalog:
  name: "Enterprise Infrastructure Catalog"
  description: "Approved infrastructure services for development teams"
  version: "1.2.0"
  
  categories:
    - name: "Compute"
      services:
        - id: "ec2-standard"
          name: "EC2 Standard Instance"
          description: "General purpose EC2 instance with standard configurations"
          template: "terraform/compute/ec2-standard.tf"
          parameters:
            - name: "instance_name"
              type: "string"
              required: true
              description: "Name of the EC2 instance"
            
            - name: "instance_type"
              type: "string"
              default: "t3.medium"
              allowed_values: ["t3.small", "t3.medium", "t3.large"]
              description: "EC2 instance type"
            
            - name: "environment"
              type: "string"
              required: true
              allowed_values: ["dev", "test", "staging", "prod"]
              description: "Deployment environment"
          
          policies:
            - "baseline-security"
            - "cost-optimization"
          
          approvals:
            dev: "automatic"
            test: "automatic"
            staging: "team-lead"
            prod: "change-advisory-board"
        
        - id: "eks-cluster"
          name: "EKS Cluster"
          description: "Managed Kubernetes cluster with standard configurations"
          template: "terraform/compute/eks-cluster.tf"
          # Additional parameters, policies, and approvals...
    
    - name: "Storage"
      services:
        - id: "s3-bucket"
          name: "S3 Bucket"
          description: "S3 bucket with standard security configurations"
          template: "terraform/storage/s3-bucket.tf"
          # Additional parameters, policies, and approvals...
        
        - id: "rds-postgres"
          name: "RDS PostgreSQL"
          description: "Managed PostgreSQL database"
          template: "terraform/storage/rds-postgres.tf"
          # Additional parameters, policies, and approvals...
    
    # Additional categories and services...
```

### 2. Self-Service Portal API

```python
# Example of a Self-Service Portal API for infrastructure provisioning
@app.route('/api/v1/infrastructure/provision', methods=['POST'])
@requires_auth
def provision_infrastructure():
    # Get provisioning parameters
    params = request.get_json()
    service_id = params.get('service_id')
    parameters = params.get('parameters', {})
    
    # Validate request
    service = ServiceCatalog.find_service(service_id)
    if not service:
        return jsonify({'error': 'Service not found'}), 404
    
    # Validate parameters
    validation_result = service.validate_parameters(parameters)
    if not validation_result.is_valid:
        return jsonify({'error': 'Invalid parameters', 'details': validation_result.errors}), 400
    
    # Check policies
    policy_result = PolicyEngine.evaluate(
        service=service,
        parameters=parameters,
        user=current_user
    )
    
    if not policy_result.is_compliant:
        return jsonify({'error': 'Policy violation', 'details': policy_result.violations}), 403
    
    # Check approvals
    environment = parameters.get('environment', 'dev')
    approval_required = service.requires_approval(environment)
    
    if approval_required:
        # Create approval request
        approval_request = ApprovalRequest.create(
            service=service,
            parameters=parameters,
            requested_by=current_user.id
        )
        
        return jsonify({
            'status': 'pending_approval',
            'request_id': approval_request.id,
            'approvers': approval_request.approvers
        }), 202
    
    # Create provisioning task
    task = InfrastructureProvisioningTask.create(
        service=service,
        parameters=parameters,
        requested_by=current_user.id
    )
    
    # Execute task asynchronously
    task_id = task_engine.execute_async(task)
    
    # Return task ID for status tracking
    return jsonify({
        'task_id': task_id,
        'status': 'provisioning',
        'service': service.name
    }), 202
```

### 3. Infrastructure Template Example

```hcl
# Terraform template for a standardized RDS PostgreSQL instance
# File: terraform/storage/rds-postgres.tf

variable "db_name" {
  description = "Name of the database"
  type        = string
}

variable "environment" {
  description = "Deployment environment"
  type        = string
  validation {
    condition     = contains(["dev", "test", "staging", "prod"], var.environment)
    error_message = "Environment must be one of: dev, test, staging, prod."
  }
}

variable "instance_class" {
  description = "Database instance class"
  type        = string
  default     = "db.t3.medium"
}

variable "storage_gb" {
  description = "Allocated storage in GB"
  type        = number
  default     = 20
  validation {
    condition     = var.storage_gb >= 20 && var.storage_gb <= 100
    error_message = "Storage must be between 20 and 100 GB."
  }
}

locals {
  # Define environment-specific configurations
  env_config = {
    dev = {
      multi_az                = false
      backup_retention_period = 1
      deletion_protection     = false
    }
    test = {
      multi_az                = false
      backup_retention_period = 3
      deletion_protection     = false
    }
    staging = {
      multi_az                = true
      backup_retention_period = 7
      deletion_protection     = true
    }
    prod = {
      multi_az                = true
      backup_retention_period = 30
      deletion_protection     = true
    }
  }
  
  # Apply environment-specific configurations
  config = local.env_config[var.environment]
}

resource "aws_db_instance" "postgres" {
  identifier           = "${var.db_name}-${var.environment}"
  engine               = "postgres"
  engine_version       = "13.7"
  instance_class       = var.instance_class
  allocated_storage    = var.storage_gb
  name                 = var.db_name
  username             = "dbadmin"
  password             = random_password.db_password.result
  parameter_group_name = aws_db_parameter_group.postgres.name
  
  # Environment-specific configurations
  multi_az                = local.config.multi_az
  backup_retention_period = local.config.backup_retention_period
  deletion_protection     = local.config.deletion_protection
  
  # Standard security configurations
  storage_encrypted      = true
  vpc_security_group_ids = [aws_security_group.db_security_group.id]
  db_subnet_group_name   = aws_db_subnet_group.db_subnet_group.name
  
  # Standard tags
  tags = {
    Name        = "${var.db_name}-${var.environment}"
    Environment = var.environment
    Service     = "RDS PostgreSQL"
    ManagedBy   = "Self-Service Infrastructure Platform"
  }
}

# Additional resources for security group, subnet group, parameter group, etc.
```

## Best Practices

1. **Define Clear Service Boundaries**: Clearly define what services are available and their limitations
2. **Implement Guardrails, Not Gates**: Use policies to guide rather than block development
3. **Provide Sensible Defaults**: Configure services with secure, optimized default settings
4. **Ensure Cost Transparency**: Make infrastructure costs visible to users
5. **Design for User Experience**: Create intuitive interfaces that developers want to use
6. **Implement Progressive Disclosure**: Show basic options first, with advanced options available when needed
7. **Automate Governance**: Embed compliance and security checks into the provisioning process
8. **Create Comprehensive Documentation**: Provide clear guidance on available services and their usage
9. **Establish Support Channels**: Define how users get help when they encounter issues
10. **Collect and Act on Feedback**: Continuously improve based on user experiences

## Common Pitfalls

1. **Excessive Customization Options**: Overwhelming users with too many choices
2. **Insufficient Abstraction**: Exposing too many underlying infrastructure details
3. **Slow Provisioning**: Creating bottlenecks in the self-service process
4. **Inadequate Testing**: Not thoroughly testing templates before making them available
5. **Poor Error Handling**: Failing to provide clear error messages and recovery paths
6. **Neglecting Deprovisioning**: Focusing only on creation without addressing cleanup
7. **Ignoring Cost Management**: Not implementing controls to prevent resource sprawl
8. **Security as an Afterthought**: Not embedding security into templates from the start
9. **Lack of Monitoring**: Not tracking usage patterns and service health
10. **Insufficient Documentation**: Not providing clear guidance on service usage

## Learning Outcomes

After studying Self-Service Infrastructure, you should be able to:

1. Design a self-service infrastructure platform that balances developer autonomy with organizational control
2. Create standardized infrastructure templates that enforce best practices
3. Implement policy-based governance for infrastructure provisioning
4. Develop approval workflows appropriate for different environments and resource types
5. Build intuitive interfaces for infrastructure self-service
6. Integrate cost management and visibility into self-service platforms
7. Establish monitoring and metrics for self-service infrastructure usage
8. Create a service catalog that meets the needs of development teams
9. Implement security controls that don't impede developer productivity
10. Develop a strategy for evolving self-service offerings based on user feedback
