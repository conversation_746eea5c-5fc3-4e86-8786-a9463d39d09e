# Internal Developer Platforms

**Skill Level: Intermediate**

## Notes

Internal Developer Platforms (IDPs) are self-service platforms built by platform engineering teams to enable developers to self-serve infrastructure and deployments. They abstract away the complexity of underlying infrastructure and provide a streamlined developer experience, allowing development teams to focus on building applications rather than managing infrastructure.

### Key Concepts:

1. **Platform as a Product**:
   - Treating the platform as a product with users (developers)
   - Focusing on developer experience and usability
   - Applying product management principles to platform development
   - Continuously improving based on user feedback

2. **Self-Service Capabilities**:
   - Enabling developers to provision resources without DevOps intervention
   - Providing intuitive interfaces (UI, CLI, API) for resource management
   - Automating common workflows and reducing manual steps
   - Standardizing deployment processes

3. **Golden Paths**:
   - Predefined, well-supported paths for common development workflows
   - Standardized templates and patterns for application development
   - Balance between standardization and flexibility
   - Reducing cognitive load for developers

4. **Abstraction Layers**:
   - Hiding infrastructure complexity behind simple interfaces
   - Providing higher-level abstractions for common tasks
   - Translating developer intent into infrastructure configurations
   - Maintaining flexibility for advanced use cases

5. **Platform Scope**:
   - Infrastructure provisioning and management
   - CI/CD pipeline configuration and execution
   - Environment management (dev, test, staging, production)
   - Observability and monitoring integration
   - Security and compliance enforcement

6. **Platform Architecture**:
   - Control plane for managing platform resources
   - Configuration management system
   - Integration with underlying infrastructure providers
   - API-first design for extensibility
   - User interfaces (web UI, CLI, API)

## Practical Example: Building an Internal Developer Platform

### 1. IDP Architecture

```mermaid
graph TD
    A[Developer] -->|Interacts with| B[Platform Portal]
    A -->|Uses| C[CLI Tool]
    A -->|Calls| D[Platform API]
    
    B --> D
    C --> D
    
    D -->|Manages| E[Configuration Repository]
    D -->|Orchestrates| F[CI/CD System]
    D -->|Provisions| G[Infrastructure]
    D -->|Monitors| H[Applications]
    
    E -->|Defines| G
    F -->|Deploys to| G
    G -->|Runs| H
    
    subgraph "Control Plane"
        D
        I[Authentication & Authorization]
        J[Workflow Engine]
        K[Template Engine]
        L[Policy Engine]
    end
    
    subgraph "Data Plane"
        G
        H
        M[Databases]
        N[Message Queues]
        O[Storage]
    end
    
    D --> I
    D --> J
    D --> K
    D --> L
```

### 2. Platform API Implementation

```python
# app.py - FastAPI-based Platform API
from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import yaml
import os
import subprocess
import json
import logging
from datetime import datetime

# Initialize FastAPI app
app = FastAPI(
    title="Internal Developer Platform API",
    description="API for managing applications and infrastructure",
    version="1.0.0"
)

# Setup authentication
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# Models
class Application(BaseModel):
    name: str
    repository: str
    team: str
    language: str
    framework: Optional[str] = None
    description: Optional[str] = None

class Environment(BaseModel):
    name: str
    application: str
    tier: str  # dev, test, staging, production
    replicas: int = 1
    resources: Dict[str, Any] = {"cpu": "100m", "memory": "256Mi"}
    variables: Dict[str, str] = {}
    domain: Optional[str] = None

class Deployment(BaseModel):
    application: str
    environment: str
    version: str
    status: str = "pending"
    deployed_at: Optional[datetime] = None

# In-memory storage (would be a database in production)
applications = {}
environments = {}
deployments = {}

# Authentication dependency
async def get_current_user(token: str = Depends(oauth2_scheme)):
    # In a real implementation, validate the token and return the user
    # For this example, we'll just return a dummy user
    return {"username": "developer", "teams": ["team-a", "team-b"]}

# Routes
@app.post("/applications/", response_model=Application)
async def create_application(application: Application, current_user: dict = Depends(get_current_user)):
    """Create a new application in the platform"""
    if application.name in applications:
        raise HTTPException(status_code=400, detail="Application already exists")
    
    # In a real implementation, validate the repository, check permissions, etc.
    
    # Store the application
    applications[application.name] = application.dict()
    
    # Create application configuration in Git repository
    create_application_config(application)
    
    return application

@app.get("/applications/", response_model=List[Application])
async def list_applications(current_user: dict = Depends(get_current_user)):
    """List all applications the user has access to"""
    # In a real implementation, filter by user's teams/permissions
    return [Application(**app) for app in applications.values()]

@app.post("/environments/", response_model=Environment)
async def create_environment(environment: Environment, current_user: dict = Depends(get_current_user)):
    """Create a new environment for an application"""
    if environment.application not in applications:
        raise HTTPException(status_code=404, detail="Application not found")
    
    env_key = f"{environment.application}-{environment.name}"
    if env_key in environments:
        raise HTTPException(status_code=400, detail="Environment already exists")
    
    # In a real implementation, validate the environment configuration
    
    # Store the environment
    environments[env_key] = environment.dict()
    
    # Provision the environment infrastructure
    provision_environment(environment)
    
    return environment

@app.post("/deployments/", response_model=Deployment)
async def create_deployment(deployment: Deployment, current_user: dict = Depends(get_current_user)):
    """Deploy an application version to an environment"""
    if deployment.application not in applications:
        raise HTTPException(status_code=404, detail="Application not found")
    
    env_key = f"{deployment.application}-{deployment.environment}"
    if env_key not in environments:
        raise HTTPException(status_code=404, detail="Environment not found")
    
    # Generate a unique deployment ID
    deployment_id = f"{deployment.application}-{deployment.environment}-{deployment.version}"
    
    # Store the deployment
    deployment_data = deployment.dict()
    deployment_data["id"] = deployment_id
    deployment_data["deployed_at"] = datetime.now()
    deployments[deployment_id] = deployment_data
    
    # Trigger the deployment pipeline
    trigger_deployment(deployment)
    
    return Deployment(**deployment_data)

@app.get("/deployments/", response_model=List[Deployment])
async def list_deployments(
    application: Optional[str] = None,
    environment: Optional[str] = None,
    current_user: dict = Depends(get_current_user)
):
    """List deployments, optionally filtered by application and/or environment"""
    result = []
    
    for deployment in deployments.values():
        if application and deployment["application"] != application:
            continue
        if environment and deployment["environment"] != environment:
            continue
        result.append(Deployment(**deployment))
    
    return result

# Helper functions
def create_application_config(application: Application):
    """Create application configuration in Git repository"""
    # In a real implementation, this would create configuration files in a Git repository
    logging.info(f"Creating configuration for application: {application.name}")
    
    # Example: Create a directory structure for the application
    os.makedirs(f"config/{application.name}", exist_ok=True)
    
    # Create a basic application manifest
    manifest = {
        "name": application.name,
        "repository": application.repository,
        "team": application.team,
        "language": application.language,
        "framework": application.framework,
        "description": application.description
    }
    
    with open(f"config/{application.name}/manifest.yaml", "w") as f:
        yaml.dump(manifest, f)
    
    logging.info(f"Created configuration for application: {application.name}")

def provision_environment(environment: Environment):
    """Provision infrastructure for an environment"""
    # In a real implementation, this would use Terraform, Pulumi, or similar
    logging.info(f"Provisioning environment: {environment.name} for application: {environment.application}")
    
    # Example: Create environment configuration
    app_dir = f"config/{environment.application}"
    os.makedirs(f"{app_dir}/environments", exist_ok=True)
    
    # Create environment manifest
    manifest = {
        "name": environment.name,
        "tier": environment.tier,
        "replicas": environment.replicas,
        "resources": environment.resources,
        "variables": environment.variables,
        "domain": environment.domain
    }
    
    with open(f"{app_dir}/environments/{environment.name}.yaml", "w") as f:
        yaml.dump(manifest, f)
    
    # In a real implementation, this would trigger infrastructure provisioning
    logging.info(f"Provisioned environment: {environment.name} for application: {environment.application}")

def trigger_deployment(deployment: Deployment):
    """Trigger a deployment pipeline"""
    # In a real implementation, this would trigger a CI/CD pipeline
    logging.info(f"Triggering deployment: {deployment.application} version {deployment.version} to {deployment.environment}")
    
    # Example: Update deployment status
    deployment_id = f"{deployment.application}-{deployment.environment}-{deployment.version}"
    deployments[deployment_id]["status"] = "in_progress"
    
    # In a real implementation, this would call a CI/CD system API
    # For this example, we'll just simulate a successful deployment
    deployments[deployment_id]["status"] = "completed"
    
    logging.info(f"Deployment completed: {deployment.application} version {deployment.version} to {deployment.environment}")

# Main entry point
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

### 3. Platform Portal UI

```typescript
// src/components/ApplicationForm.tsx
import React, { useState } from 'react';
import { Form, Input, Select, Button, notification } from 'antd';
import { createApplication } from '../api/applications';

const { Option } = Select;

interface ApplicationFormProps {
  onSuccess: () => void;
}

const ApplicationForm: React.FC<ApplicationFormProps> = ({ onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      await createApplication(values);
      notification.success({
        message: 'Application Created',
        description: `Application ${values.name} has been created successfully.`,
      });
      form.resetFields();
      onSuccess();
    } catch (error) {
      notification.error({
        message: 'Creation Failed',
        description: error.message || 'Failed to create application.',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form form={form} layout="vertical" onFinish={handleSubmit}>
      <Form.Item
        name="name"
        label="Application Name"
        rules={[{ required: true, message: 'Please enter application name' }]}
      >
        <Input placeholder="my-application" />
      </Form.Item>

      <Form.Item
        name="repository"
        label="Git Repository"
        rules={[{ required: true, message: 'Please enter git repository URL' }]}
      >
        <Input placeholder="https://github.com/organization/repo.git" />
      </Form.Item>

      <Form.Item
        name="team"
        label="Team"
        rules={[{ required: true, message: 'Please select a team' }]}
      >
        <Select placeholder="Select a team">
          <Option value="team-a">Team A</Option>
          <Option value="team-b">Team B</Option>
          <Option value="team-c">Team C</Option>
        </Select>
      </Form.Item>

      <Form.Item
        name="language"
        label="Programming Language"
        rules={[{ required: true, message: 'Please select a language' }]}
      >
        <Select placeholder="Select a language">
          <Option value="javascript">JavaScript</Option>
          <Option value="typescript">TypeScript</Option>
          <Option value="python">Python</Option>
          <Option value="java">Java</Option>
          <Option value="go">Go</Option>
        </Select>
      </Form.Item>

      <Form.Item name="framework" label="Framework">
        <Input placeholder="React, Django, Spring Boot, etc." />
      </Form.Item>

      <Form.Item name="description" label="Description">
        <Input.TextArea rows={4} placeholder="Brief description of the application" />
      </Form.Item>

      <Form.Item>
        <Button type="primary" htmlType="submit" loading={loading}>
          Create Application
        </Button>
      </Form.Item>
    </Form>
  );
};

export default ApplicationForm;
```

### 4. Platform CLI Tool

```python
# platform_cli.py
import click
import requests
import os
import json
import yaml
from tabulate import tabulate
from datetime import datetime

# Configuration
API_URL = os.environ.get("PLATFORM_API_URL", "http://localhost:8000")
TOKEN = os.environ.get("PLATFORM_API_TOKEN", "")

# API client
class PlatformClient:
    def __init__(self, api_url, token):
        self.api_url = api_url
        self.headers = {"Authorization": f"Bearer {token}"}
    
    def list_applications(self):
        response = requests.get(f"{self.api_url}/applications/", headers=self.headers)
        response.raise_for_status()
        return response.json()
    
    def create_application(self, app_data):
        response = requests.post(
            f"{self.api_url}/applications/",
            headers=self.headers,
            json=app_data
        )
        response.raise_for_status()
        return response.json()
    
    def list_environments(self, application=None):
        params = {}
        if application:
            params["application"] = application
        
        response = requests.get(
            f"{self.api_url}/environments/",
            headers=self.headers,
            params=params
        )
        response.raise_for_status()
        return response.json()
    
    def create_environment(self, env_data):
        response = requests.post(
            f"{self.api_url}/environments/",
            headers=self.headers,
            json=env_data
        )
        response.raise_for_status()
        return response.json()
    
    def list_deployments(self, application=None, environment=None):
        params = {}
        if application:
            params["application"] = application
        if environment:
            params["environment"] = environment
        
        response = requests.get(
            f"{self.api_url}/deployments/",
            headers=self.headers,
            params=params
        )
        response.raise_for_status()
        return response.json()
    
    def create_deployment(self, deployment_data):
        response = requests.post(
            f"{self.api_url}/deployments/",
            headers=self.headers,
            json=deployment_data
        )
        response.raise_for_status()
        return response.json()

# CLI commands
@click.group()
def cli():
    """Command-line interface for the Internal Developer Platform"""
    pass

# Application commands
@cli.group()
def app():
    """Manage applications"""
    pass

@app.command("list")
def list_applications():
    """List all applications"""
    client = PlatformClient(API_URL, TOKEN)
    applications = client.list_applications()
    
    if not applications:
        click.echo("No applications found.")
        return
    
    table_data = []
    for app in applications:
        table_data.append([
            app["name"],
            app["team"],
            app["language"],
            app.get("framework", ""),
            app.get("description", "")
        ])
    
    click.echo(tabulate(
        table_data,
        headers=["Name", "Team", "Language", "Framework", "Description"],
        tablefmt="grid"
    ))

@app.command("create")
@click.option("--name", required=True, help="Application name")
@click.option("--repo", required=True, help="Git repository URL")
@click.option("--team", required=True, help="Team name")
@click.option("--language", required=True, help="Programming language")
@click.option("--framework", help="Framework")
@click.option("--description", help="Application description")
def create_application(name, repo, team, language, framework, description):
    """Create a new application"""
    client = PlatformClient(API_URL, TOKEN)
    
    app_data = {
        "name": name,
        "repository": repo,
        "team": team,
        "language": language
    }
    
    if framework:
        app_data["framework"] = framework
    
    if description:
        app_data["description"] = description
    
    try:
        result = client.create_application(app_data)
        click.echo(f"Application '{name}' created successfully.")
    except requests.exceptions.HTTPError as e:
        click.echo(f"Error creating application: {e.response.text}", err=True)

# Environment commands
@cli.group()
def env():
    """Manage environments"""
    pass

@env.command("list")
@click.option("--app", help="Filter by application name")
def list_environments(app):
    """List environments"""
    client = PlatformClient(API_URL, TOKEN)
    environments = client.list_environments(application=app)
    
    if not environments:
        click.echo("No environments found.")
        return
    
    table_data = []
    for env in environments:
        table_data.append([
            env["name"],
            env["application"],
            env["tier"],
            env["replicas"],
            env.get("domain", "")
        ])
    
    click.echo(tabulate(
        table_data,
        headers=["Name", "Application", "Tier", "Replicas", "Domain"],
        tablefmt="grid"
    ))

@env.command("create")
@click.option("--name", required=True, help="Environment name")
@click.option("--app", required=True, help="Application name")
@click.option("--tier", required=True, type=click.Choice(["dev", "test", "staging", "production"]), help="Environment tier")
@click.option("--replicas", type=int, default=1, help="Number of replicas")
@click.option("--cpu", default="100m", help="CPU request")
@click.option("--memory", default="256Mi", help="Memory request")
@click.option("--domain", help="Custom domain")
@click.option("--vars", help="Environment variables (JSON string)")
def create_environment(name, app, tier, replicas, cpu, memory, domain, vars):
    """Create a new environment"""
    client = PlatformClient(API_URL, TOKEN)
    
    env_data = {
        "name": name,
        "application": app,
        "tier": tier,
        "replicas": replicas,
        "resources": {
            "cpu": cpu,
            "memory": memory
        }
    }
    
    if domain:
        env_data["domain"] = domain
    
    if vars:
        try:
            env_data["variables"] = json.loads(vars)
        except json.JSONDecodeError:
            click.echo("Error: Environment variables must be a valid JSON string.", err=True)
            return
    
    try:
        result = client.create_environment(env_data)
        click.echo(f"Environment '{name}' for application '{app}' created successfully.")
    except requests.exceptions.HTTPError as e:
        click.echo(f"Error creating environment: {e.response.text}", err=True)

# Deployment commands
@cli.group()
def deploy():
    """Manage deployments"""
    pass

@deploy.command("list")
@click.option("--app", help="Filter by application name")
@click.option("--env", help="Filter by environment name")
def list_deployments(app, env):
    """List deployments"""
    client = PlatformClient(API_URL, TOKEN)
    deployments = client.list_deployments(application=app, environment=env)
    
    if not deployments:
        click.echo("No deployments found.")
        return
    
    table_data = []
    for dep in deployments:
        deployed_at = dep.get("deployed_at", "")
        if deployed_at:
            deployed_at = datetime.fromisoformat(deployed_at.replace("Z", "+00:00")).strftime("%Y-%m-%d %H:%M:%S")
        
        table_data.append([
            dep["application"],
            dep["environment"],
            dep["version"],
            dep["status"],
            deployed_at
        ])
    
    click.echo(tabulate(
        table_data,
        headers=["Application", "Environment", "Version", "Status", "Deployed At"],
        tablefmt="grid"
    ))

@deploy.command("create")
@click.option("--app", required=True, help="Application name")
@click.option("--env", required=True, help="Environment name")
@click.option("--version", required=True, help="Version to deploy")
def create_deployment(app, env, version):
    """Deploy an application version to an environment"""
    client = PlatformClient(API_URL, TOKEN)
    
    deployment_data = {
        "application": app,
        "environment": env,
        "version": version
    }
    
    try:
        result = client.create_deployment(deployment_data)
        click.echo(f"Deployment of '{app}' version '{version}' to '{env}' initiated successfully.")
        click.echo(f"Status: {result['status']}")
    except requests.exceptions.HTTPError as e:
        click.echo(f"Error creating deployment: {e.response.text}", err=True)

if __name__ == "__main__":
    cli()
```

## Best Practices

1. **Focus on Developer Experience**:
   - Design intuitive interfaces with developer workflows in mind
   - Minimize cognitive load by abstracting complexity
   - Provide comprehensive documentation and examples
   - Collect and act on developer feedback

2. **Implement Golden Paths**:
   - Create standardized templates for common application types
   - Provide clear, well-documented paths for common tasks
   - Make the right way the easy way
   - Allow escape hatches for advanced users

3. **Balance Standardization and Flexibility**:
   - Standardize common patterns and workflows
   - Allow customization where needed
   - Provide different levels of abstraction for different users
   - Support both simple and complex use cases

4. **Design for Self-Service**:
   - Enable developers to provision resources without waiting
   - Automate approval workflows where necessary
   - Implement guardrails rather than gates
   - Provide clear feedback on actions

5. **Adopt API-First Design**:
   - Build a robust API as the foundation
   - Create UIs and CLIs on top of the API
   - Enable programmatic access for automation
   - Document the API comprehensively

6. **Implement Comprehensive Observability**:
   - Provide visibility into platform usage and performance
   - Enable application-level observability
   - Create dashboards for different stakeholders
   - Implement alerting for platform issues

7. **Enforce Security and Compliance**:
   - Build security into platform workflows
   - Implement policy as code
   - Automate compliance checks
   - Provide security scanning and reporting

8. **Evolve Incrementally**:
   - Start with high-value, high-impact capabilities
   - Gather feedback and iterate
   - Add capabilities based on user needs
   - Continuously improve existing features

## Common Pitfalls

1. **Over-Engineering**:
   - Building complex solutions before understanding user needs
   - Adding features that developers don't need
   - Creating overly complex abstractions
   - Focusing on technical elegance over usability

2. **Insufficient Abstraction**:
   - Exposing too much infrastructure complexity to developers
   - Requiring deep infrastructure knowledge to use the platform
   - Creating leaky abstractions that break in edge cases
   - Not providing enough guardrails

3. **Lack of Documentation**:
   - Not providing clear documentation for platform features
   - Missing examples and tutorials
   - Unclear error messages and troubleshooting guides
   - Not explaining the "why" behind platform decisions

4. **Ignoring Developer Feedback**:
   - Building the platform without developer input
   - Not collecting usage metrics and feedback
   - Failing to iterate based on user needs
   - Prioritizing platform team convenience over developer experience

5. **Rigid Standardization**:
   - Enforcing standards without understanding developer needs
   - Not providing escape hatches for valid edge cases
   - Creating bottlenecks through excessive approval processes
   - Prioritizing standardization over developer productivity

6. **Poor Performance**:
   - Slow provisioning and deployment processes
   - Unresponsive user interfaces
   - Inefficient resource utilization
   - Lack of performance monitoring and optimization

7. **Inadequate Testing**:
   - Not testing platform changes thoroughly
   - Missing integration tests with underlying infrastructure
   - Failing to test different user scenarios
   - Not having a staging environment for platform changes

8. **Neglecting Platform Evolution**:
   - Building the platform once and considering it "done"
   - Not adapting to changing developer needs
   - Failing to incorporate new technologies and practices
   - Not measuring and improving platform effectiveness

## Learning Outcomes

After studying internal developer platforms, you should be able to:

1. Explain the purpose and benefits of internal developer platforms
2. Design platform architectures that balance standardization and flexibility
3. Implement self-service capabilities for infrastructure and deployments
4. Create golden paths for common development workflows
5. Build effective abstractions that hide complexity while maintaining power
6. Design and implement platform APIs, UIs, and CLIs
7. Integrate platforms with underlying infrastructure and CI/CD systems
8. Apply product management principles to platform development
9. Measure and improve developer experience and platform effectiveness
10. Evolve platforms incrementally based on user feedback and changing needs
