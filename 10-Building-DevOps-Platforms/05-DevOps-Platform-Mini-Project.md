# DevOps Platform Mini-Project

**Skill Level: Advanced**

## Notes

This mini-project challenges you to design and implement a simplified Internal Developer Platform (IDP) that demonstrates key concepts in platform engineering. The project will integrate various components covered in this module, including self-service infrastructure, API management, and platform engineering principles.

### Project Objectives

1. Design a cohesive platform architecture
2. Implement self-service capabilities for developers
3. Create standardized templates for infrastructure and application deployment
4. Build a simple developer portal
5. Implement basic policy controls
6. Demonstrate the platform's capabilities through a sample application

### Project Components

1. **Platform Architecture**: Overall design of the platform components
2. **Service Catalog**: Curated collection of infrastructure services
3. **Developer Portal**: Interface for developers to interact with the platform
4. **Infrastructure Templates**: Standardized infrastructure definitions
5. **CI/CD Integration**: Automated deployment pipelines
6. **Policy Engine**: Rules for governance and compliance
7. **Observability Integration**: Monitoring and logging capabilities

## Practical Example: Building a Simplified IDP

### 1. Platform Architecture

```mermaid
graph TD
    A[Developer] -->|Uses| B[Developer Portal]
    A -->|Uses| C[CLI Tool]
    
    B --> D[Platform API]
    C --> D
    
    D -->|Manages| E[Service Catalog]
    D -->|Triggers| F[CI/CD Pipelines]
    D -->|Provisions| G[Infrastructure]
    D -->|Configures| H[Monitoring]
    
    E --> I[Infrastructure Templates]
    E --> J[Application Templates]
    
    F --> K[GitHub Actions/Jenkins]
    G --> L[Kubernetes/Cloud Resources]
    H --> M[Prometheus/Grafana]
    
    N[Policy Engine] -->|Validates| D
    O[Authentication Service] -->|Secures| D
```

### 2. Project Implementation Plan

```yaml
# project-plan.yaml
name: "Mini-IDP Project"
timeline: "4 weeks"
phases:
  - name: "Design"
    duration: "1 week"
    tasks:
      - "Define platform architecture"
      - "Design service catalog structure"
      - "Design developer portal UI mockups"
      - "Define API specifications"
      - "Create infrastructure template specifications"
  
  - name: "Core Implementation"
    duration: "2 weeks"
    tasks:
      - "Set up Kubernetes cluster for platform components"
      - "Implement platform API (basic functionality)"
      - "Create initial service catalog with 3-5 services"
      - "Implement infrastructure provisioning with Terraform"
      - "Set up CI/CD integration with GitHub Actions"
      - "Implement basic developer portal UI"
  
  - name: "Advanced Features"
    duration: "1 week"
    tasks:
      - "Implement policy engine with OPA"
      - "Add monitoring integration with Prometheus/Grafana"
      - "Implement service mesh integration with Istio"
      - "Add cost tracking and reporting"
      - "Implement audit logging"
  
  - name: "Testing and Documentation"
    duration: "Ongoing"
    tasks:
      - "Create comprehensive API documentation"
      - "Write user guides for the developer portal"
      - "Develop example workflows for common tasks"
      - "Create demo application that uses the platform"
      - "Prepare final presentation and demonstration"
```

### 3. Service Catalog Implementation

```yaml
# service-catalog.yaml
apiVersion: platform.example.com/v1
kind: ServiceCatalog
metadata:
  name: mini-idp-catalog
spec:
  categories:
    - name: "Compute"
      services:
        - id: "web-application"
          name: "Web Application"
          description: "Standard web application with frontend and backend"
          template: "templates/web-application.yaml"
          parameters:
            - name: "application_name"
              type: "string"
              required: true
              description: "Name of the application"
            
            - name: "environment"
              type: "string"
              default: "development"
              allowed_values: ["development", "staging", "production"]
              description: "Deployment environment"
            
            - name: "instance_count"
              type: "integer"
              default: 2
              min: 1
              max: 10
              description: "Number of application instances"
          
          policies:
            - "resource-limits"
            - "security-baseline"
    
    - name: "Data"
      services:
        - id: "postgres-database"
          name: "PostgreSQL Database"
          description: "Managed PostgreSQL database"
          template: "templates/postgres-database.yaml"
          parameters:
            - name: "database_name"
              type: "string"
              required: true
              description: "Name of the database"
            
            - name: "storage_gb"
              type: "integer"
              default: 10
              min: 5
              max: 100
              description: "Storage size in GB"
            
            - name: "high_availability"
              type: "boolean"
              default: false
              description: "Enable high availability"
          
          policies:
            - "data-protection"
            - "backup-required"
    
    - name: "Networking"
      services:
        - id: "api-gateway"
          name: "API Gateway"
          description: "API Gateway for service exposure"
          template: "templates/api-gateway.yaml"
          parameters:
            - name: "service_name"
              type: "string"
              required: true
              description: "Name of the service to expose"
            
            - name: "path_prefix"
              type: "string"
              required: true
              description: "API path prefix"
            
            - name: "rate_limit"
              type: "integer"
              default: 100
              description: "Rate limit per minute"
          
          policies:
            - "security-baseline"
            - "api-standards"
```

### 4. Infrastructure Template Example

```yaml
# templates/web-application.yaml
apiVersion: platform.example.com/v1
kind: InfrastructureTemplate
metadata:
  name: web-application
spec:
  resources:
    - apiVersion: apps/v1
      kind: Deployment
      metadata:
        name: "{{.application_name}}"
        labels:
          app: "{{.application_name}}"
          environment: "{{.environment}}"
      spec:
        replicas: "{{.instance_count}}"
        selector:
          matchLabels:
            app: "{{.application_name}}"
        template:
          metadata:
            labels:
              app: "{{.application_name}}"
          spec:
            containers:
              - name: "{{.application_name}}"
                image: "{{.image | default 'nginx:latest'}}"
                ports:
                  - containerPort: 80
                resources:
                  requests:
                    memory: "{{.memory_request | default '128Mi'}}"
                    cpu: "{{.cpu_request | default '100m'}}"
                  limits:
                    memory: "{{.memory_limit | default '256Mi'}}"
                    cpu: "{{.cpu_limit | default '200m'}}"
                readinessProbe:
                  httpGet:
                    path: /health
                    port: 80
                  initialDelaySeconds: 10
                  periodSeconds: 5
    
    - apiVersion: v1
      kind: Service
      metadata:
        name: "{{.application_name}}"
      spec:
        selector:
          app: "{{.application_name}}"
        ports:
          - port: 80
            targetPort: 80
    
    - apiVersion: networking.k8s.io/v1
      kind: Ingress
      metadata:
        name: "{{.application_name}}"
        annotations:
          kubernetes.io/ingress.class: nginx
      spec:
        rules:
          - host: "{{.application_name}}.{{.environment}}.example.com"
            http:
              paths:
                - path: /
                  pathType: Prefix
                  backend:
                    service:
                      name: "{{.application_name}}"
                      port:
                        number: 80
    
    - apiVersion: monitoring.coreos.com/v1
      kind: ServiceMonitor
      metadata:
        name: "{{.application_name}}"
      spec:
        selector:
          matchLabels:
            app: "{{.application_name}}"
        endpoints:
          - port: http
            interval: 15s
```

### 5. Platform API Implementation

```python
# app.py - Flask-based Platform API
from flask import Flask, request, jsonify
import yaml
import os
import uuid
import subprocess
import json
from kubernetes import client, config

app = Flask(__name__)

# Load service catalog
with open('service-catalog.yaml', 'r') as f:
    service_catalog = yaml.safe_load(f)

# Initialize Kubernetes client
config.load_kube_config()
k8s_client = client.CustomObjectsApi()

@app.route('/api/v1/catalog', methods=['GET'])
def get_catalog():
    return jsonify(service_catalog)

@app.route('/api/v1/services/<service_id>', methods=['GET'])
def get_service(service_id):
    for category in service_catalog['spec']['categories']:
        for service in category['services']:
            if service['id'] == service_id:
                return jsonify(service)
    
    return jsonify({'error': 'Service not found'}), 404

@app.route('/api/v1/provision', methods=['POST'])
def provision_service():
    data = request.get_json()
    service_id = data.get('service_id')
    parameters = data.get('parameters', {})
    
    # Find service in catalog
    service = None
    for category in service_catalog['spec']['categories']:
        for svc in category['services']:
            if svc['id'] == service_id:
                service = svc
                break
        if service:
            break
    
    if not service:
        return jsonify({'error': 'Service not found'}), 404
    
    # Validate parameters
    for param in service['parameters']:
        if param.get('required', False) and param['name'] not in parameters:
            return jsonify({'error': f"Required parameter '{param['name']}' is missing"}), 400
        
        if param['name'] in parameters:
            value = parameters[param['name']]
            
            # Validate allowed values
            if 'allowed_values' in param and value not in param['allowed_values']:
                return jsonify({'error': f"Parameter '{param['name']}' must be one of: {param['allowed_values']}"}), 400
            
            # Validate min/max for integers
            if param['type'] == 'integer':
                if 'min' in param and value < param['min']:
                    return jsonify({'error': f"Parameter '{param['name']}' must be at least {param['min']}"}), 400
                if 'max' in param and value > param['max']:
                    return jsonify({'error': f"Parameter '{param['name']}' must be at most {param['max']}"}), 400
    
    # Load template
    template_path = service['template']
    with open(template_path, 'r') as f:
        template = yaml.safe_load(f)
    
    # Generate instance ID
    instance_id = str(uuid.uuid4())
    
    # Process template (simplified for example)
    # In a real implementation, you would use a proper templating engine
    resources = []
    for resource in template['spec']['resources']:
        # Apply parameters to resource (simplified)
        resource_yaml = yaml.dump(resource)
        for key, value in parameters.items():
            resource_yaml = resource_yaml.replace(f"{{{{.{key}}}}}", str(value))
        
        # Parse processed YAML
        processed_resource = yaml.safe_load(resource_yaml)
        resources.append(processed_resource)
    
    # Create resources in Kubernetes
    created_resources = []
    for resource in resources:
        try:
            # This is simplified - you would need proper API calls for each resource type
            api_version = resource['apiVersion']
            kind = resource['kind']
            metadata = resource['metadata']
            spec = resource['spec']
            
            # Create resource (example for custom resources)
            response = k8s_client.create_namespaced_custom_object(
                group=api_version.split('/')[0],
                version=api_version.split('/')[1],
                namespace='default',
                plural=kind.lower() + 's',
                body=resource
            )
            
            created_resources.append({
                'kind': kind,
                'name': metadata['name'],
                'status': 'created'
            })
        except Exception as e:
            # In a real implementation, you would handle errors and potentially roll back
            return jsonify({'error': str(e)}), 500
    
    # Return success response
    return jsonify({
        'instance_id': instance_id,
        'service_id': service_id,
        'status': 'provisioned',
        'resources': created_resources
    }), 201

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=8080)
```

## Project Deliverables

For this mini-project, you should create and submit the following:

1. **Architecture Documentation**:
   - Platform architecture diagram
   - Component descriptions and interactions
   - Design decisions and rationale

2. **Implementation**:
   - Service catalog definition
   - Infrastructure templates
   - Platform API code
   - Developer portal (UI or CLI)
   - Policy definitions

3. **Demo Application**:
   - Sample application that uses the platform
   - Deployment instructions
   - Usage examples

4. **Documentation**:
   - Installation guide
   - User guide
   - API documentation
   - Troubleshooting guide

## Best Practices

1. **Start Simple**: Begin with core functionality and expand incrementally
2. **Use Existing Tools**: Leverage existing open-source tools rather than building everything from scratch
3. **Focus on Developer Experience**: Design the platform with developers as the primary users
4. **Implement Proper Error Handling**: Provide clear error messages and recovery paths
5. **Use Infrastructure as Code**: Define all infrastructure components using IaC
6. **Document Everything**: Create comprehensive documentation for all aspects of the platform
7. **Implement Proper Testing**: Test all components thoroughly
8. **Consider Security**: Implement security controls from the beginning
9. **Design for Extensibility**: Make it easy to add new services and capabilities
10. **Gather Feedback**: Test with potential users and incorporate their feedback

## Common Pitfalls

1. **Scope Creep**: Trying to implement too many features
2. **Over-Engineering**: Making the platform more complex than necessary
3. **Neglecting User Experience**: Focusing on technical capabilities at the expense of usability
4. **Insufficient Error Handling**: Not providing clear error messages and recovery paths
5. **Poor Documentation**: Not documenting the platform adequately
6. **Ignoring Security**: Not implementing proper security controls
7. **Tight Coupling**: Creating dependencies between components that should be independent
8. **Inadequate Testing**: Not testing the platform thoroughly
9. **Neglecting Observability**: Not implementing proper monitoring and logging
10. **Reinventing the Wheel**: Building components that could be leveraged from existing tools

## Learning Outcomes

After completing this mini-project, you should be able to:

1. Design a cohesive platform architecture that meets developer needs
2. Implement self-service capabilities for infrastructure and application deployment
3. Create standardized templates that enforce best practices
4. Build interfaces that provide a good developer experience
5. Implement policy controls that enforce organizational standards
6. Integrate the platform with CI/CD pipelines
7. Implement observability for platform components
8. Document the platform effectively for different audiences
9. Evaluate the trade-offs between different platform design decisions
10. Understand the challenges and benefits of platform engineering
