# Security Architecture and Threat Modeling

**Skill Level: Advanced**

## Notes

Security architecture and threat modeling are essential practices for DevOps Architects to identify, understand, and mitigate security risks early in the development lifecycle. By systematically analyzing potential threats and designing security controls, you can build more resilient systems that protect against evolving attack vectors.

### Key Security Architecture Concepts:

1. **Defense in Depth**:
   - Multiple layers of security controls
   - Redundant security mechanisms
   - Compensating controls
   - No single point of failure

2. **Zero Trust Architecture**:
   - Never trust, always verify
   - Least privilege access
   - Micro-segmentation
   - Continuous verification

3. **Secure by Design Principles**:
   - Security built into architecture
   - Secure defaults
   - Fail securely
   - Economy of mechanism (simplicity)
   - Complete mediation (check every access)

4. **Security Boundaries**:
   - Trust boundaries
   - Network segmentation
   - Data classification boundaries
   - Control planes vs. data planes

### Threat Modeling Process:

```mermaid
graph TD
    A[Identify Assets] --> B[Create Architecture Diagram]
    B --> C[Identify Threat Actors]
    C --> D[Identify Attack Vectors]
    D --> E[Analyze Threats using Framework]
    E --> F[Prioritize Risks]
    F --> G[Define Mitigations]
    G --> H[Validate Controls]
    H --> I[Document Findings]
    I --> J[Implement Controls]
    J --> K[Continuous Reassessment]
    K --> A
```

## Practical Example: Threat Modeling for a Microservices Application

### 1. System Architecture Diagram

```mermaid
graph TD
    subgraph Internet
        User[User Browser]
    end
    
    subgraph DMZ
        ALB[Application Load Balancer]
        WAF[Web Application Firewall]
    end
    
    subgraph "VPC - Public Subnets"
        API[API Gateway]
        CDN[Content Delivery Network]
    end
    
    subgraph "VPC - Private Subnets"
        Auth[Authentication Service]
        Orders[Order Service]
        Products[Product Service]
        Users[User Service]
        Payments[Payment Service]
    end
    
    subgraph "VPC - Database Subnets"
        AuthDB[(Auth Database)]
        OrderDB[(Order Database)]
        ProductDB[(Product Database)]
        UserDB[(User Database)]
    end
    
    subgraph "External Services"
        PaymentGateway[Payment Gateway]
        EmailService[Email Service]
    end
    
    User --> WAF
    WAF --> ALB
    ALB --> API
    ALB --> CDN
    
    API --> Auth
    API --> Orders
    API --> Products
    API --> Users
    
    Auth --> AuthDB
    Orders --> OrderDB
    Products --> ProductDB
    Users --> UserDB
    
    Orders --> Payments
    Payments --> PaymentGateway
    Orders --> EmailService
    
    %% Trust Boundaries
    classDef trustBoundary fill:#f96,stroke:#333,stroke-width:2px
    class Internet,DMZ,VPC - Public Subnets,VPC - Private Subnets,VPC - Database Subnets,External Services trustBoundary
```

### 2. STRIDE Threat Analysis

```yaml
# STRIDE Threat Model for Authentication Service
service: Authentication Service
assets:
  - User credentials
  - Session tokens
  - Authentication logic
  - User database

threats:
  - category: Spoofing
    description: Attacker impersonates a legitimate user
    attack_vectors:
      - Credential theft through phishing
      - Session hijacking
      - Man-in-the-middle attacks
    mitigations:
      - Implement MFA
      - Use TLS for all communications
      - Implement proper session management
      - Set secure and HttpOnly flags on cookies
    risk_level: High

  - category: Tampering
    description: Attacker modifies authentication data
    attack_vectors:
      - SQL injection
      - API parameter tampering
      - Client-side validation bypass
    mitigations:
      - Input validation
      - Parameterized queries
      - Server-side validation
      - Integrity checks
    risk_level: Medium

  - category: Repudiation
    description: User denies performing an action
    attack_vectors:
      - Lack of audit logging
      - Timestamp manipulation
      - Log tampering
    mitigations:
      - Implement comprehensive audit logging
      - Use tamper-evident logging
      - Centralized log management
      - Digital signatures for critical actions
    risk_level: Medium

  - category: Information Disclosure
    description: Exposure of sensitive authentication data
    attack_vectors:
      - Verbose error messages
      - Insecure storage of credentials
      - Unencrypted data transmission
    mitigations:
      - Generic error messages
      - Password hashing with strong algorithms
      - Encryption in transit and at rest
      - Data minimization
    risk_level: High

  - category: Denial of Service
    description: Authentication service unavailability
    attack_vectors:
      - Login brute force
      - Resource exhaustion
      - API flooding
    mitigations:
      - Rate limiting
      - CAPTCHA for repeated failures
      - Account lockout policies
      - Auto-scaling infrastructure
    risk_level: Medium

  - category: Elevation of Privilege
    description: Gaining higher privileges than authorized
    attack_vectors:
      - JWT token manipulation
      - Authorization bypass
      - Insecure direct object references
    mitigations:
      - Signed JWT tokens with short expiration
      - Role-based access control
      - Principle of least privilege
      - Regular permission audits
    risk_level: Critical
```

### 3. Security Controls Implementation

```terraform
# Network Security Controls in Terraform
resource "aws_security_group" "api_gateway" {
  name        = "api-gateway-sg"
  description = "Security group for API Gateway"
  vpc_id      = aws_vpc.main.id

  # Allow HTTPS inbound from WAF only
  ingress {
    from_port       = 443
    to_port         = 443
    protocol        = "tcp"
    security_groups = [aws_security_group.waf.id]
  }

  # Allow outbound to microservices only
  egress {
    from_port       = 8080
    to_port         = 8080
    protocol        = "tcp"
    security_groups = [
      aws_security_group.auth_service.id,
      aws_security_group.order_service.id,
      aws_security_group.product_service.id,
      aws_security_group.user_service.id
    ]
  }

  tags = {
    Name = "api-gateway-sg"
    Environment = "production"
  }
}

resource "aws_security_group" "auth_service" {
  name        = "auth-service-sg"
  description = "Security group for Authentication Service"
  vpc_id      = aws_vpc.main.id

  # Allow inbound from API Gateway only
  ingress {
    from_port       = 8080
    to_port         = 8080
    protocol        = "tcp"
    security_groups = [aws_security_group.api_gateway.id]
  }

  # Allow outbound to Auth DB only
  egress {
    from_port       = 5432
    to_port         = 5432
    protocol        = "tcp"
    security_groups = [aws_security_group.auth_db.id]
  }

  tags = {
    Name = "auth-service-sg"
    Environment = "production"
  }
}

# Database encryption for sensitive data
resource "aws_db_instance" "auth_db" {
  allocated_storage    = 20
  storage_type         = "gp2"
  engine               = "postgres"
  engine_version       = "13.4"
  instance_class       = "db.t3.medium"
  name                 = "auth_db"
  username             = var.db_username
  password             = var.db_password
  parameter_group_name = "default.postgres13"
  skip_final_snapshot  = false
  
  # Security controls
  storage_encrypted        = true
  kms_key_id               = aws_kms_key.db_encryption_key.arn
  multi_az                 = true
  backup_retention_period  = 7
  deletion_protection      = true
  vpc_security_group_ids   = [aws_security_group.auth_db.id]
  db_subnet_group_name     = aws_db_subnet_group.database.name
  
  # Enhanced monitoring
  monitoring_interval      = 30
  monitoring_role_arn      = aws_iam_role.rds_monitoring_role.arn
  
  tags = {
    Name = "auth-db"
    Environment = "production"
    DataClassification = "sensitive"
  }
}

# WAF rules to protect against common attacks
resource "aws_wafv2_web_acl" "main" {
  name        = "main-web-acl"
  description = "Web ACL for API protection"
  scope       = "REGIONAL"

  default_action {
    allow {}
  }

  # SQL injection protection
  rule {
    name     = "SQLi-rule"
    priority = 1

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesSQLiRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "SQLi-rule"
      sampled_requests_enabled   = true
    }
  }

  # XSS protection
  rule {
    name     = "XSS-rule"
    priority = 2

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesCommonRuleSet"
        vendor_name = "AWS"

        excluded_rule {
          name = "SizeRestrictions_BODY"
        }
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "XSS-rule"
      sampled_requests_enabled   = true
    }
  }

  # Rate limiting rule
  rule {
    name     = "RateLimit-rule"
    priority = 3

    action {
      block {}
    }

    statement {
      rate_based_statement {
        limit              = 1000
        aggregate_key_type = "IP"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "RateLimit-rule"
      sampled_requests_enabled   = true
    }
  }

  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "main-web-acl"
    sampled_requests_enabled   = true
  }
}
```

### 4. Authentication Service Security Implementation

```java
@Service
public class AuthenticationServiceImpl implements AuthenticationService {
    
    private static final Logger logger = LoggerFactory.getLogger(AuthenticationServiceImpl.class);
    private static final int MAX_LOGIN_ATTEMPTS = 5;
    private static final long ACCOUNT_LOCKOUT_DURATION_MS = 30 * 60 * 1000; // 30 minutes
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Autowired
    private JwtTokenProvider jwtTokenProvider;
    
    @Autowired
    private LoginAttemptService loginAttemptService;
    
    @Autowired
    private AuditService auditService;
    
    @Override
    public AuthResponse authenticate(AuthRequest request) {
        String username = request.getUsername();
        String ipAddress = request.getIpAddress();
        
        // Check for brute force attempts
        if (loginAttemptService.isBlocked(ipAddress)) {
            logger.warn("Authentication attempt from blocked IP: {}", ipAddress);
            auditService.logSecurityEvent("BLOCKED_AUTH_ATTEMPT", username, ipAddress);
            throw new AccountLockedException("Too many failed attempts. Try again later.");
        }
        
        try {
            // Find user by username (email)
            User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new UsernameNotFoundException("User not found"));
            
            // Check if account is locked
            if (user.isAccountLocked()) {
                if (System.currentTimeMillis() - user.getLockTime() > ACCOUNT_LOCKOUT_DURATION_MS) {
                    // Unlock account if lockout period has passed
                    user.setAccountLocked(false);
                    user.setFailedAttempts(0);
                    userRepository.save(user);
                } else {
                    logger.warn("Attempt to access locked account: {}", username);
                    auditService.logSecurityEvent("LOCKED_ACCOUNT_ACCESS_ATTEMPT", username, ipAddress);
                    throw new AccountLockedException("Account is locked. Try again later.");
                }
            }
            
            // Validate password
            if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
                // Increment failed attempts
                loginAttemptService.loginFailed(ipAddress);
                
                // Update user's failed attempts
                if (user.getFailedAttempts() + 1 >= MAX_LOGIN_ATTEMPTS) {
                    user.setAccountLocked(true);
                    user.setLockTime(System.currentTimeMillis());
                    logger.warn("Account locked due to multiple failed attempts: {}", username);
                } else {
                    user.setFailedAttempts(user.getFailedAttempts() + 1);
                }
                
                userRepository.save(user);
                auditService.logSecurityEvent("FAILED_LOGIN", username, ipAddress);
                throw new BadCredentialsException("Invalid username or password");
            }
            
            // Reset failed attempts on successful login
            loginAttemptService.loginSucceeded(ipAddress);
            user.setFailedAttempts(0);
            userRepository.save(user);
            
            // Check if MFA is required
            if (user.isMfaEnabled()) {
                return new AuthResponse(null, true, user.getMfaMethod());
            }
            
            // Generate JWT token
            String token = jwtTokenProvider.createToken(username, user.getRoles());
            
            // Log successful authentication
            auditService.logSecurityEvent("SUCCESSFUL_LOGIN", username, ipAddress);
            
            return new AuthResponse(token, false, null);
        } catch (UsernameNotFoundException | BadCredentialsException e) {
            // Don't reveal whether username or password was incorrect
            throw new BadCredentialsException("Invalid username or password");
        }
    }
    
    @Override
    public AuthResponse verifyMfaAndAuthenticate(MfaVerificationRequest request) {
        // MFA verification logic
        // ...
    }
}
```

## Best Practices

1. **Integrate Threat Modeling Early**:
   - Perform threat modeling during design phase
   - Update threat models when architecture changes
   - Make threat modeling a regular practice
   - Train development teams on threat identification

2. **Use Structured Threat Modeling Approaches**:
   - Apply frameworks like STRIDE, PASTA, or OCTAVE
   - Use threat modeling tools to streamline the process
   - Create reusable threat model templates
   - Maintain a threat library for common components

3. **Focus on Critical Assets and Threats**:
   - Identify and prioritize crown jewel assets
   - Focus on high-impact threats first
   - Consider likelihood and impact in risk assessment
   - Address critical vulnerabilities promptly

4. **Design Defense in Depth**:
   - Implement multiple security layers
   - Don't rely on a single security control
   - Apply security at network, host, application, and data levels
   - Implement compensating controls

5. **Apply Least Privilege**:
   - Grant minimal permissions needed
   - Use time-bound access when possible
   - Implement just-in-time access for privileged operations
   - Regularly review and audit permissions

6. **Secure Data Throughout Its Lifecycle**:
   - Classify data based on sensitivity
   - Encrypt data in transit and at rest
   - Implement data access controls
   - Apply data minimization principles

7. **Design for Failure and Recovery**:
   - Assume security controls will fail
   - Implement detection mechanisms
   - Create incident response procedures
   - Design for rapid recovery

## Common Pitfalls

1. **Treating Threat Modeling as a One-Time Activity**:
   - Not updating threat models as systems evolve
   - Performing threat modeling too late in development
   - Not revisiting threats after significant changes
   - Treating threat modeling as a compliance checkbox

2. **Focusing Only on Technical Threats**:
   - Ignoring business logic vulnerabilities
   - Missing insider threat scenarios
   - Overlooking physical security considerations
   - Not considering social engineering attacks

3. **Inadequate Threat Prioritization**:
   - Trying to address all threats equally
   - Not considering business impact
   - Missing critical threats while focusing on minor issues
   - Lack of risk-based approach

4. **Siloed Security Architecture**:
   - Security design disconnected from overall architecture
   - Security as an afterthought or bolt-on
   - Lack of collaboration between security and development
   - Security requirements not integrated with functional requirements

5. **Overreliance on Perimeter Security**:
   - Focusing too much on external threats
   - Soft interior once perimeter is breached
   - Inadequate internal segmentation
   - Not implementing zero trust principles

6. **Neglecting Usability in Security Design**:
   - Implementing controls that users will bypass
   - Excessive security friction
   - Not considering user experience
   - Security controls that impede business functions

7. **Insufficient Documentation**:
   - Poorly documented security architecture
   - Missing security requirements
   - Undocumented assumptions
   - Lack of traceability from threats to controls

## Learning Outcomes

After studying security architecture and threat modeling, you should be able to:

1. Conduct effective threat modeling sessions for complex systems
2. Identify and prioritize security threats based on risk
3. Design comprehensive security architectures with defense in depth
4. Apply zero trust principles to modern application architectures
5. Integrate security controls throughout the technology stack
6. Balance security requirements with system functionality and usability
7. Document security architecture decisions and their rationale
8. Evaluate the effectiveness of security controls against identified threats
