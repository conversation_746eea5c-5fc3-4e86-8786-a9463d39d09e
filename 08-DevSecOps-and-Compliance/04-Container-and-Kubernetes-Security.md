# Container and Kubernetes Security

**Skill Level: Advanced**

## Notes

Container and Kubernetes security is a critical concern for DevOps Architects as organizations increasingly adopt containerization for application deployment. Securing the entire container lifecycle and Kubernetes infrastructure requires a comprehensive approach that addresses vulnerabilities at each layer of the stack.

### Key Container Security Concepts:

1. **Container Image Security**:
   - Vulnerability scanning
   - Base image selection and hardening
   - Image signing and verification
   - Image registry security

2. **Container Runtime Security**:
   - Container isolation
   - Resource limitations
   - Privileged containers
   - Runtime vulnerability detection

3. **Container Orchestration Security**:
   - Kubernetes API server security
   - RBAC (Role-Based Access Control)
   - Network policies
   - Pod security policies/standards

4. **Supply Chain Security**:
   - Software Bill of Materials (SBOM)
   - Dependency scanning
   - Chain of custody
   - Provenance verification

### Container Security Layers:

```mermaid
graph TD
    A[Application Code] --> B[Application Dependencies]
    B --> C[Container Image]
    C --> D[Container Runtime]
    D --> E[Orchestration Platform]
    E --> F[Host OS]
    F --> G[Infrastructure]
    
    subgraph "Security Controls"
        H[SAST/SCA] --> A
        I[Dependency Scanning] --> B
        J[Image Scanning] --> C
        K[Runtime Security] --> D
        L[Kubernetes Security] --> E
        M[OS Hardening] --> F
        N[Infrastructure Security] --> G
    end
```

## Practical Example: Implementing Container and Kubernetes Security

### 1. Secure Container Image Building with Dockerfile Best Practices

```dockerfile
# Use specific version of minimal base image
FROM alpine:3.16.2 AS builder

# Set working directory
WORKDIR /app

# Add non-root user
RUN addgroup -S appgroup && adduser -S appuser -G appgroup

# Install only necessary dependencies with version pinning
RUN apk add --no-cache \
    python3=3.10.5-r0 \
    py3-pip=22.1.1-r0 \
    curl=7.83.1-r3

# Copy requirements first to leverage Docker cache
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY --chown=appuser:appgroup . .

# Multi-stage build to create minimal final image
FROM alpine:3.16.2

# Install runtime dependencies only
RUN apk add --no-cache \
    python3=3.10.5-r0 \
    curl=7.83.1-r3 && \
    addgroup -S appgroup && adduser -S appuser -G appgroup

# Set working directory
WORKDIR /app

# Copy from builder stage
COPY --from=builder --chown=appuser:appgroup /app /app

# Set non-root user
USER appuser

# Define healthcheck
HEALTHCHECK --interval=30s --timeout=3s \
  CMD curl -f http://localhost:8080/health || exit 1

# Use specific port
EXPOSE 8080

# Define entrypoint and command separately
ENTRYPOINT ["python3"]
CMD ["app.py"]
```

### 2. Kubernetes Security with Pod Security Standards and Network Policies

```yaml
# Namespace with Pod Security Standards
apiVersion: v1
kind: Namespace
metadata:
  name: secure-application
  labels:
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted

---
# Secure Pod Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: secure-app
  namespace: secure-application
spec:
  replicas: 3
  selector:
    matchLabels:
      app: secure-app
  template:
    metadata:
      labels:
        app: secure-app
    spec:
      # Security Context for Pod
      securityContext:
        fsGroup: 1000
        runAsNonRoot: true
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: secure-app
        image: my-registry.io/secure-app:1.2.3
        imagePullPolicy: Always
        # Security Context for Container
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          privileged: false
          readOnlyRootFilesystem: true
          runAsUser: 1000
        # Resource limits
        resources:
          limits:
            cpu: "500m"
            memory: "512Mi"
          requests:
            cpu: "100m"
            memory: "128Mi"
        # Mount temporary storage for writable files
        volumeMounts:
        - name: tmp-volume
          mountPath: /tmp
        # Liveness and readiness probes
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
        ports:
        - containerPort: 8080
          name: http
      # Volumes
      volumes:
      - name: tmp-volume
        emptyDir: {}
      # Image pull secrets
      imagePullSecrets:
      - name: registry-credentials

---
# Network Policy to restrict traffic
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: secure-app-network-policy
  namespace: secure-application
spec:
  podSelector:
    matchLabels:
      app: secure-app
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: frontend
    - podSelector:
        matchLabels:
          role: frontend
    ports:
    - protocol: TCP
      port: 8080
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: database
    - podSelector:
        matchLabels:
          role: database
    ports:
    - protocol: TCP
      port: 5432
  - to:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9090
```

### 3. Kubernetes RBAC Configuration

```yaml
# Service Account with limited permissions
apiVersion: v1
kind: ServiceAccount
metadata:
  name: app-service-account
  namespace: secure-application

---
# Role with specific permissions
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: app-role
  namespace: secure-application
rules:
- apiGroups: [""]
  resources: ["pods", "services"]
  verbs: ["get", "list"]
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get"]
  resourceNames: ["app-config", "app-secrets"]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create"]

---
# RoleBinding to associate Role with ServiceAccount
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: app-role-binding
  namespace: secure-application
subjects:
- kind: ServiceAccount
  name: app-service-account
  namespace: secure-application
roleRef:
  kind: Role
  name: app-role
  apiGroup: rbac.authorization.k8s.io

---
# ClusterRole for monitoring
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: monitoring-role
rules:
- apiGroups: [""]
  resources: ["pods", "nodes", "services"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["metrics.k8s.io"]
  resources: ["pods", "nodes"]
  verbs: ["get", "list", "watch"]

---
# ClusterRoleBinding for monitoring
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: monitoring-role-binding
subjects:
- kind: ServiceAccount
  name: monitoring-service-account
  namespace: monitoring
roleRef:
  kind: ClusterRole
  name: monitoring-role
  apiGroup: rbac.authorization.k8s.io
```

### 4. Container Image Scanning in CI/CD Pipeline

```yaml
# GitLab CI/CD Pipeline with Container Scanning
stages:
  - build
  - test
  - scan
  - deploy

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""
  SECURE_LOG_LEVEL: info

# Build container image
build:
  stage: build
  image: docker:20.10.16
  services:
    - docker:20.10.16-dind
  script:
    - docker build -t $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA .
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
  artifacts:
    paths:
      - Dockerfile

# Run container vulnerability scanning
container_scanning:
  stage: scan
  image: 
    name: aquasec/trivy:latest
    entrypoint: [""]
  variables:
    TRIVY_USERNAME: $CI_REGISTRY_USER
    TRIVY_PASSWORD: $CI_REGISTRY_PASSWORD
    TRIVY_AUTH_URL: $CI_REGISTRY
    TRIVY_NO_PROGRESS: "true"
    TRIVY_CACHE_DIR: .trivycache/
  script:
    - trivy --version
    # Scan for vulnerabilities
    - trivy image --exit-code 0 --format template --template "@/contrib/gitlab.tpl" -o gl-container-scanning-report.json $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
    # Fail on critical vulnerabilities
    - trivy image --exit-code 1 --severity CRITICAL $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
  cache:
    paths:
      - .trivycache/
  artifacts:
    reports:
      container_scanning: gl-container-scanning-report.json

# Run software composition analysis
dependency_scanning:
  stage: scan
  image: 
    name: aquasec/trivy:latest
    entrypoint: [""]
  script:
    - trivy fs --exit-code 0 --format template --template "@/contrib/gitlab.tpl" -o gl-dependency-scanning-report.json .
  artifacts:
    reports:
      dependency_scanning: gl-dependency-scanning-report.json

# Deploy to Kubernetes with security checks
deploy:
  stage: deploy
  image: bitnami/kubectl:latest
  script:
    # Validate Kubernetes manifests
    - kubectl apply --dry-run=client -f k8s/
    # Check for security issues in manifests
    - |
      if [ -x "$(command -v kubesec)" ]; then
        kubesec scan k8s/deployment.yaml
      fi
    # Deploy if all checks pass
    - kubectl apply -f k8s/
  environment:
    name: production
  only:
    - main
```

### 5. Runtime Security with Falco

```yaml
# Falco Kubernetes Deployment
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: falco
  namespace: security
  labels:
    app: falco
    role: security
spec:
  selector:
    matchLabels:
      app: falco
  template:
    metadata:
      labels:
        app: falco
    spec:
      serviceAccountName: falco
      containers:
      - name: falco
        image: falcosecurity/falco:0.32.1
        securityContext:
          privileged: true
        args:
          - /usr/bin/falco
          - -K
          - /var/run/secrets/kubernetes.io/serviceaccount/token
          - -k
          - https://kubernetes.default.svc.cluster.local
          - -pk
        volumeMounts:
        - mountPath: /host/var/run/docker.sock
          name: docker-socket
          readOnly: true
        - mountPath: /host/dev
          name: dev-fs
        - mountPath: /host/proc
          name: proc-fs
          readOnly: true
        - mountPath: /host/boot
          name: boot-fs
          readOnly: true
        - mountPath: /host/lib/modules
          name: lib-modules
          readOnly: true
        - mountPath: /host/usr
          name: usr-fs
          readOnly: true
        - mountPath: /etc/falco
          name: falco-config
      volumes:
      - name: docker-socket
        hostPath:
          path: /var/run/docker.sock
      - name: dev-fs
        hostPath:
          path: /dev
      - name: proc-fs
        hostPath:
          path: /proc
      - name: boot-fs
        hostPath:
          path: /boot
      - name: lib-modules
        hostPath:
          path: /lib/modules
      - name: usr-fs
        hostPath:
          path: /usr
      - name: falco-config
        configMap:
          name: falco-config

---
# Falco custom rules ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: falco-config
  namespace: security
data:
  falco_rules.yaml: |-
    - rule: Terminal shell in container
      desc: A shell was spawned by a pod in the container
      condition: >
        container.id != host and
        proc.name = bash and
        container
      output: >
        Shell spawned in a container (user=%user.name container_id=%container.id
        container_name=%container.name pod_name=%k8s.pod.name namespace=%k8s.ns.name)
      priority: WARNING
      tags: [container, shell]

    - rule: Privilege Escalation Using Sudo
      desc: Detect privilege escalation using sudo command
      condition: >
        spawned_process and
        proc.name = "sudo" and
        container
      output: >
        Privilege escalation using sudo in container (user=%user.name command=%proc.cmdline
        container_id=%container.id container_name=%container.name pod_name=%k8s.pod.name namespace=%k8s.ns.name)
      priority: WARNING
      tags: [container, privilege-escalation]

    - rule: Package Management Process in Container
      desc: Package management process executed in container
      condition: >
        spawned_process and
        (proc.name in (apk, apt, apt-get, pip, pip3, npm, gem, yum, dnf)) and
        container and
        not container.image.repository in (docker.io/library/node, docker.io/library/python)
      output: >
        Package management process executed in container (user=%user.name command=%proc.cmdline
        container_id=%container.id container_name=%container.name pod_name=%k8s.pod.name namespace=%k8s.ns.name)
      priority: WARNING
      tags: [container, process]
```

## Best Practices

1. **Secure the Container Supply Chain**:
   - Use minimal and trusted base images
   - Pin dependencies to specific versions
   - Implement image signing and verification
   - Scan images for vulnerabilities before deployment

2. **Implement Least Privilege**:
   - Run containers as non-root users
   - Use read-only file systems where possible
   - Drop unnecessary capabilities
   - Avoid privileged containers

3. **Secure Kubernetes Configuration**:
   - Enable RBAC with fine-grained permissions
   - Implement Pod Security Standards
   - Use network policies to restrict traffic
   - Secure etcd with encryption and authentication

4. **Implement Runtime Security**:
   - Deploy runtime security monitoring
   - Implement behavioral analysis
   - Set up alerts for suspicious activities
   - Use admission controllers for policy enforcement

5. **Secure Secrets Management**:
   - Use dedicated secrets management solutions
   - Avoid storing secrets in container images
   - Implement secret rotation
   - Limit secret access to specific pods

6. **Implement Network Segmentation**:
   - Use network policies to restrict pod communication
   - Implement service mesh for mTLS
   - Isolate sensitive workloads
   - Control egress traffic

7. **Regular Security Scanning and Updates**:
   - Continuously scan images and running containers
   - Implement automated patching
   - Regularly update Kubernetes components
   - Monitor for new vulnerabilities

## Common Pitfalls

1. **Neglecting Image Security**:
   - Using outdated or vulnerable base images
   - Not scanning images for vulnerabilities
   - Using images from untrusted sources
   - Not implementing image signing and verification

2. **Excessive Container Privileges**:
   - Running containers as root
   - Using privileged containers unnecessarily
   - Mounting sensitive host paths
   - Not restricting capabilities

3. **Weak Kubernetes RBAC**:
   - Using cluster-admin roles too broadly
   - Not implementing least privilege
   - Inadequate service account management
   - Missing regular permission audits

4. **Inadequate Network Security**:
   - Missing network policies
   - Open communication between all pods
   - Unencrypted pod-to-pod traffic
   - Unrestricted egress traffic

5. **Poor Secrets Management**:
   - Hardcoding secrets in Dockerfiles or manifests
   - Storing secrets in ConfigMaps
   - Not rotating secrets
   - Oversharing secrets across pods

6. **Neglecting Runtime Security**:
   - Focusing only on build-time security
   - Missing behavioral monitoring
   - No anomaly detection
   - Inadequate logging and auditing

7. **Outdated Components**:
   - Running outdated Kubernetes versions
   - Not patching container images
   - Neglecting node security updates
   - Using deprecated APIs and features

## Learning Outcomes

After studying container and Kubernetes security, you should be able to:

1. Design secure container images and build processes
2. Implement comprehensive Kubernetes security controls
3. Configure appropriate RBAC permissions for different workloads
4. Design network segmentation using Kubernetes network policies
5. Implement runtime security monitoring and response
6. Secure the container supply chain from development to deployment
7. Evaluate and mitigate container and Kubernetes security risks
8. Implement security best practices across the container lifecycle
