# Compliance as Code

**Skill Level: Intermediate**

## Notes

Compliance as Code (CaC) is an approach that applies DevOps principles to compliance management, treating compliance requirements as code that can be versioned, tested, and automatically enforced. As a DevOps Architect, implementing CaC allows you to build compliance into your systems from the beginning rather than treating it as an afterthought.

### Key Compliance as Code Concepts:

1. **Codified Compliance Requirements**:
   - Translate regulatory requirements into code
   - Create machine-readable policies
   - Version control compliance rules
   - Automate compliance validation

2. **Continuous Compliance Validation**:
   - Integrate compliance checks into CI/CD pipelines
   - Perform automated compliance testing
   - Generate compliance reports automatically
   - Implement real-time compliance monitoring

3. **Compliance Frameworks and Standards**:
   - Industry-specific regulations (HIPAA, PCI DSS, GDPR, SOC2, etc.)
   - Security frameworks (NIST, ISO 27001, CIS)
   - Cloud provider compliance tools
   - Open-source compliance tools

4. **Compliance Documentation and Evidence**:
   - Automated evidence collection
   - Audit-ready documentation
   - Compliance dashboards
   - Traceability from requirements to implementation

### Compliance as Code Workflow:

```mermaid
graph TD
    A[Define Compliance Requirements] --> B[Translate to Code]
    B --> C[Implement Automated Tests]
    C --> D[Integrate in CI/CD]
    D --> E[Continuous Monitoring]
    E --> F[Automated Reporting]
    F --> G[Audit and Review]
    G --> A
    
    H[Regulatory Changes] --> A
    I[Security Incidents] --> A
    J[System Changes] --> C
    K[New Technologies] --> B
```

## Practical Example: Implementing Compliance as Code

### 1. AWS Compliance Checks with Terraform and OPA

```hcl
# Define AWS S3 bucket with compliance requirements
resource "aws_s3_bucket" "compliant_bucket" {
  bucket = "compliant-data-bucket"
  acl    = "private"
  
  # Enforce server-side encryption (required for compliance)
  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        sse_algorithm = "AES256"
      }
    }
  }
  
  # Enable versioning (required for compliance)
  versioning {
    enabled = true
  }
  
  # Enable logging (required for compliance)
  logging {
    target_bucket = aws_s3_bucket.log_bucket.id
    target_prefix = "log/compliant-data-bucket/"
  }
  
  # Block public access (required for compliance)
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
  
  # Add compliance tags
  tags = {
    Compliance  = "PCI-DSS,GDPR"
    DataClass   = "Sensitive"
    Environment = "Production"
  }
}

# OPA policy to validate S3 bucket compliance
# File: s3_compliance.rego
package terraform.analysis

import input.tfplan as tfplan

# Deny S3 buckets without encryption
deny[msg] {
  resource := tfplan.resource_changes[_]
  resource.type == "aws_s3_bucket"
  resource.change.after.server_side_encryption_configuration == null
  
  msg := sprintf(
    "S3 bucket '%s' is missing server-side encryption configuration",
    [resource.change.after.bucket]
  )
}

# Deny S3 buckets without versioning
deny[msg] {
  resource := tfplan.resource_changes[_]
  resource.type == "aws_s3_bucket"
  resource.change.after.versioning[_].enabled != true
  
  msg := sprintf(
    "S3 bucket '%s' must have versioning enabled",
    [resource.change.after.bucket]
  )
}

# Deny S3 buckets with public access
deny[msg] {
  resource := tfplan.resource_changes[_]
  resource.type == "aws_s3_bucket"
  resource.change.after.block_public_acls != true
  
  msg := sprintf(
    "S3 bucket '%s' must block public ACLs",
    [resource.change.after.bucket]
  )
}
```

### 2. Kubernetes Compliance with Gatekeeper

```yaml
# PodSecurityPolicy-like constraints with OPA Gatekeeper
apiVersion: constraints.gatekeeper.sh/v1beta1
kind: K8sPSPPrivilegedContainer
metadata:
  name: psp-privileged-container
spec:
  match:
    kinds:
      - apiGroups: [""]
        kinds: ["Pod"]
    excludedNamespaces: ["kube-system"]
  parameters: {}
---
apiVersion: constraints.gatekeeper.sh/v1beta1
kind: K8sPSPAllowPrivilegeEscalationContainer
metadata:
  name: psp-allow-privilege-escalation
spec:
  match:
    kinds:
      - apiGroups: [""]
        kinds: ["Pod"]
    excludedNamespaces: ["kube-system"]
  parameters:
    allowPrivilegeEscalation: false
---
# Enforce resource limits for compliance
apiVersion: constraints.gatekeeper.sh/v1beta1
kind: K8sRequiredResources
metadata:
  name: container-must-have-limits
spec:
  match:
    kinds:
      - apiGroups: [""]
        kinds: ["Pod"]
  parameters:
    limits: ["memory", "cpu"]
---
# Enforce required labels for compliance tracking
apiVersion: constraints.gatekeeper.sh/v1beta1
kind: K8sRequiredLabels
metadata:
  name: deployment-must-have-compliance-labels
spec:
  match:
    kinds:
      - apiGroups: ["apps"]
        kinds: ["Deployment"]
  parameters:
    labels: ["compliance-level", "data-classification", "owner"]
```

### 3. Compliance Scanning with InSpec

```ruby
# PCI DSS compliance profile for Linux systems
control 'pci-dss-requirement-2.2.4' do
  impact 1.0
  title 'Remove all unnecessary functionality'
  desc 'Remove all unnecessary functionality, such as scripts, drivers, features, subsystems, file systems, and unnecessary web servers.'
  
  describe package('telnet') do
    it { should_not be_installed }
  end
  
  describe package('rsh-server') do
    it { should_not be_installed }
  end
  
  describe package('ypserv') do
    it { should_not be_installed }
  end
  
  describe package('tftp-server') do
    it { should_not be_installed }
  end
end

control 'pci-dss-requirement-6.5.2' do
  impact 1.0
  title 'Buffer overflows'
  desc 'Buffer overflows: Buffer overflows exploit vulnerabilities that allow an attacker to overwrite memory segments of an application. The result is that the attacker can insert malicious code at the end of the buffer and then execute it to take control of the system.'
  
  describe file('/etc/sysctl.conf') do
    its('content') { should match /kernel.randomize_va_space\s*=\s*2/ }
  end
  
  describe command('sysctl kernel.randomize_va_space') do
    its('stdout') { should match /kernel.randomize_va_space\s*=\s*2/ }
  end
end

control 'pci-dss-requirement-8.2.3' do
  impact 1.0
  title 'Password requirements'
  desc 'Passwords/passphrases must meet the following: Require a minimum length of at least seven characters. Contain both numeric and alphabetic characters.'
  
  describe file('/etc/security/pwquality.conf') do
    its('content') { should match /minlen\s*=\s*[7-9]|1[0-9]/ }
    its('content') { should match /minclass\s*=\s*[2-4]/ }
  end
end
```

### 4. Automated Compliance Reporting

```python
# compliance_report.py
import json
import datetime
import boto3
import csv
import os

def generate_compliance_report():
    """Generate a compliance report from various sources"""
    
    # Initialize report data
    report = {
        "report_date": datetime.datetime.now().isoformat(),
        "compliance_status": "Compliant",
        "findings": [],
        "summary": {
            "total_checks": 0,
            "passed_checks": 0,
            "failed_checks": 0,
            "compliance_score": 0
        }
    }
    
    # Collect AWS Config compliance data
    config = boto3.client('config')
    compliance_details = config.get_compliance_details_by_config_rule(
        ConfigRuleName='s3-bucket-encryption-enabled',
        ComplianceTypes=['NON_COMPLIANT']
    )
    
    for detail in compliance_details['EvaluationResults']:
        report["findings"].append({
            "resource_id": detail['EvaluationResultIdentifier']['EvaluationResultQualifier']['ResourceId'],
            "resource_type": detail['EvaluationResultIdentifier']['EvaluationResultQualifier']['ResourceType'],
            "rule": 's3-bucket-encryption-enabled',
            "status": "Failed",
            "timestamp": detail['ResultRecorderTime'].isoformat()
        })
        report["summary"]["failed_checks"] += 1
    
    # Collect InSpec results
    if os.path.exists('inspec-results.json'):
        with open('inspec-results.json', 'r') as f:
            inspec_data = json.load(f)
            
        for control in inspec_data['controls']:
            report["summary"]["total_checks"] += 1
            
            if control['status'] == 'passed':
                report["summary"]["passed_checks"] += 1
            else:
                report["findings"].append({
                    "resource_id": control.get('resource_id', 'N/A'),
                    "resource_type": "OS",
                    "rule": control['id'],
                    "status": "Failed",
                    "timestamp": datetime.datetime.now().isoformat()
                })
                report["summary"]["failed_checks"] += 1
    
    # Calculate compliance score
    if report["summary"]["total_checks"] > 0:
        report["summary"]["compliance_score"] = (report["summary"]["passed_checks"] / report["summary"]["total_checks"]) * 100
    
    # Update overall compliance status
    if report["summary"]["failed_checks"] > 0:
        report["compliance_status"] = "Non-Compliant"
    
    # Write report to file
    with open('compliance_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    # Generate CSV report for stakeholders
    with open('compliance_summary.csv', 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['Report Date', 'Compliance Status', 'Total Checks', 'Passed Checks', 'Failed Checks', 'Compliance Score'])
        writer.writerow([
            report["report_date"],
            report["compliance_status"],
            report["summary"]["total_checks"],
            report["summary"]["passed_checks"],
            report["summary"]["failed_checks"],
            f"{report['summary']['compliance_score']:.2f}%"
        ])
    
    return report

if __name__ == "__main__":
    generate_compliance_report()
```

## Best Practices

1. **Shift Left Compliance**:
   - Integrate compliance requirements into design phase
   - Implement compliance checks early in development
   - Make compliance part of developer workflow
   - Create compliance-aware architecture patterns

2. **Standardize Compliance Controls**:
   - Create reusable compliance modules
   - Implement consistent controls across environments
   - Use compliance control libraries
   - Standardize compliance tagging and metadata

3. **Automate Evidence Collection**:
   - Implement automated audit trails
   - Capture compliance evidence during deployment
   - Store evidence in immutable storage
   - Link evidence to specific compliance requirements

4. **Implement Continuous Monitoring**:
   - Deploy real-time compliance monitoring
   - Create compliance dashboards
   - Implement automated alerting for compliance violations
   - Track compliance drift over time

5. **Maintain Compliance Documentation**:
   - Generate documentation from code
   - Create compliance matrices linking controls to requirements
   - Maintain up-to-date system security plans
   - Document compliance exceptions and mitigations

6. **Collaborate Across Teams**:
   - Involve security and compliance teams early
   - Create cross-functional compliance working groups
   - Share compliance knowledge across teams
   - Establish clear compliance ownership

## Common Pitfalls

1. **Treating Compliance as a Checkbox Exercise**:
   - Focusing on passing audits rather than true security
   - Implementing controls without understanding their purpose
   - Not adapting compliance to your specific environment
   - Treating compliance as a one-time activity

2. **Manual Compliance Processes**:
   - Relying on manual reviews and approvals
   - Using spreadsheets for compliance tracking
   - Manual evidence collection during audits
   - Ad-hoc compliance validation

3. **Siloed Compliance Responsibilities**:
   - Making compliance solely the responsibility of security teams
   - Not involving developers in compliance implementation
   - Separating compliance from regular development activities
   - Lack of compliance awareness across teams

4. **Inflexible Compliance Implementation**:
   - Implementing controls that hinder innovation
   - Not adapting compliance to cloud environments
   - Using outdated compliance approaches for modern systems
   - Failing to balance compliance with usability

5. **Inadequate Compliance Monitoring**:
   - Point-in-time compliance checks only
   - Missing continuous compliance validation
   - Lack of visibility into compliance status
   - Delayed detection of compliance drift

6. **Poor Compliance Documentation**:
   - Insufficient evidence for audits
   - Unclear mapping between controls and requirements
   - Outdated system documentation
   - Inability to demonstrate compliance effectiveness

## Learning Outcomes

After studying Compliance as Code, you should be able to:

1. Translate regulatory requirements into automated compliance controls
2. Implement compliance validation in CI/CD pipelines
3. Design architectures that facilitate continuous compliance
4. Create automated compliance reporting and dashboards
5. Integrate compliance tools into DevOps workflows
6. Balance compliance requirements with development velocity
7. Prepare systems and documentation for compliance audits
8. Implement a continuous compliance monitoring strategy
