# DevSecOps Mini-Project: Secure CI/CD Pipeline Implementation

**Skill Level: Advanced**

## Project Overview

In this mini-project, you will design and implement a comprehensive DevSecOps pipeline for a microservices application. The pipeline will incorporate security at every stage of the software development lifecycle, from code commit to production deployment. You will implement security scanning, compliance checks, and automated security controls to create a secure-by-default deployment process.

## Learning Objectives

1. Design a comprehensive DevSecOps pipeline
2. Implement security scanning and testing at multiple stages
3. Configure compliance as code for automated validation
4. Secure container images and Kubernetes deployments
5. Implement security monitoring and alerting
6. Create security documentation and evidence collection

## Project Architecture

The application consists of the following components:

```mermaid
graph TD
    subgraph "Frontend"
        A[Web UI]
        B[Mobile API]
    end
    
    subgraph "Backend Services"
        C[User Service]
        D[Product Service]
        E[Order Service]
        F[Payment Service]
        G[Notification Service]
    end
    
    subgraph "Data Stores"
        H[(User DB)]
        I[(Product DB)]
        J[(Order DB)]
    end
    
    subgraph "External Services"
        K[Payment Gateway]
        L[Email Service]
    end
    
    A --> B
    B --> C
    B --> D
    B --> E
    C --> H
    D --> I
    E --> J
    E --> F
    E --> G
    F --> K
    G --> L
```

## Project Tasks

### 1. Set Up Secure CI/CD Infrastructure

Create a secure CI/CD infrastructure using GitLab CI/CD, Jenkins, or GitHub Actions.

#### Implementation Steps:

1. Configure secure runner/agent infrastructure
2. Implement secrets management for CI/CD
3. Set up secure artifact storage
4. Configure RBAC for CI/CD systems
5. Implement audit logging for CI/CD activities

#### Example GitLab CI/CD Configuration:

```yaml
# .gitlab-ci.yml
stages:
  - pre-commit
  - build
  - security-scan
  - test
  - compliance
  - deploy-dev
  - integration-test
  - deploy-prod
  - post-deploy

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""
  SECURE_LOG_LEVEL: info
  KUBERNETES_MEMORY_REQUEST: 512Mi
  KUBERNETES_MEMORY_LIMIT: 1Gi
  COMPLIANCE_IMAGE: registry.example.com/security/compliance-tools:1.2.3
  SCANNER_IMAGE: registry.example.com/security/security-scanner:1.2.3

# Use GitLab's security scanning templates
include:
  - template: Security/SAST.gitlab-ci.yml
  - template: Security/Dependency-Scanning.gitlab-ci.yml
  - template: Security/Secret-Detection.gitlab-ci.yml
  - template: Security/Container-Scanning.gitlab-ci.yml
  - template: Security/DAST.gitlab-ci.yml
  - template: Security/API-Security.gitlab-ci.yml
  - template: Security/IaC-Security.gitlab-ci.yml

# Custom pre-commit checks
pre-commit:
  stage: pre-commit
  image: python:3.10-slim
  script:
    - pip install pre-commit
    - pre-commit install
    - pre-commit run --all-files
  artifacts:
    paths:
      - pre-commit-results.txt

# Build with security flags
build:
  stage: build
  image: docker:20.10.16
  services:
    - docker:20.10.16-dind
  script:
    - docker build --build-arg BUILDKIT_INLINE_CACHE=1 --no-cache --security-opt seccomp=seccomp-profile.json -t $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA .
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
    # Generate SBOM
    - docker sbom $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA -o sbom.json
  artifacts:
    paths:
      - sbom.json

# Custom security scanning
custom-security-scan:
  stage: security-scan
  image: $SCANNER_IMAGE
  script:
    - security-scanner --image $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA --output scan-results.json
    - security-scanner --sbom sbom.json --output sbom-analysis.json
    - security-scanner --threshold high --fail-on-threshold
  artifacts:
    paths:
      - scan-results.json
      - sbom-analysis.json

# Compliance validation
compliance-check:
  stage: compliance
  image: $COMPLIANCE_IMAGE
  script:
    - compliance-scanner --config compliance-rules.yaml --output compliance-report.json
    - compliance-scanner --threshold critical --fail-on-threshold
  artifacts:
    paths:
      - compliance-report.json
    reports:
      compliance: compliance-report.json

# Secure deployment to development
deploy-dev:
  stage: deploy-dev
  image: bitnami/kubectl:latest
  script:
    - kubectl config use-context dev
    - kubectl apply -f k8s/namespace.yaml
    - kubectl apply -f k8s/network-policies.yaml
    - kubectl apply -f k8s/rbac.yaml
    - kubectl apply -f k8s/secrets.yaml
    - envsubst < k8s/deployment.yaml | kubectl apply -f -
    - kubectl rollout status deployment/app-deployment -n $CI_PROJECT_NAME
  environment:
    name: development
    url: https://dev.example.com/$CI_PROJECT_NAME
  only:
    - main

# Post-deployment security validation
post-deploy-security:
  stage: post-deploy
  image: $SCANNER_IMAGE
  script:
    - security-scanner --url https://dev.example.com/$CI_PROJECT_NAME --output post-deploy-scan.json
    - security-scanner --threshold medium --fail-on-threshold
  artifacts:
    paths:
      - post-deploy-scan.json
```

### 2. Implement Pre-Commit Security Hooks

Set up pre-commit hooks to catch security issues before code is committed.

#### Implementation Steps:

1. Configure pre-commit framework
2. Add security linters and scanners
3. Implement secrets detection
4. Add secure coding standard checks
5. Configure commit signing

#### Example Pre-Commit Configuration:

```yaml
# .pre-commit-config.yaml
repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.3.0
    hooks:
    -   id: trailing-whitespace
    -   id: end-of-file-fixer
    -   id: check-yaml
    -   id: check-added-large-files
    -   id: check-json
    -   id: detect-private-key
    -   id: check-merge-conflict

-   repo: https://github.com/zricethezav/gitleaks
    rev: v8.12.0
    hooks:
    -   id: gitleaks

-   repo: https://github.com/hadolint/hadolint
    rev: v2.10.0
    hooks:
    -   id: hadolint
        args: ['--ignore', 'DL3008', '--ignore', 'DL3013', '--ignore', 'DL3016']

-   repo: https://github.com/Lucas-C/pre-commit-hooks-nodejs
    rev: v1.1.2
    hooks:
    -   id: dockerfile_lint

-   repo: https://github.com/antonbabenko/pre-commit-terraform
    rev: v1.76.0
    hooks:
    -   id: terraform_fmt
    -   id: terraform_validate
    -   id: terraform_tflint
    -   id: terraform_tfsec

-   repo: https://github.com/Lucas-C/pre-commit-hooks-safety
    rev: v1.3.0
    hooks:
    -   id: python-safety-dependencies-check

-   repo: https://github.com/pryorda/dockerfilelint-precommit-hooks
    rev: v0.1.0
    hooks:
    -   id: dockerfilelint

-   repo: local
    hooks:
    -   id: git-secrets
        name: git-secrets
        entry: git-secrets --scan
        language: system
        pass_filenames: false
```

### 3. Create Secure Container Images

Implement secure container image building practices.

#### Implementation Steps:

1. Create a secure base image
2. Implement multi-stage builds
3. Configure minimal permissions
4. Remove unnecessary components
5. Implement image signing

#### Example Secure Dockerfile:

```dockerfile
# Build stage
FROM node:16-alpine AS builder

# Set working directory
WORKDIR /app

# Install dependencies with exact versions
COPY package*.json ./
RUN npm ci --production

# Copy source code
COPY . .

# Build application
RUN npm run build

# Security scan during build
RUN npm audit --production

# Runtime stage
FROM node:16-alpine AS runtime

# Set working directory
WORKDIR /app

# Add non-root user
RUN addgroup -S appgroup && adduser -S appuser -G appgroup

# Install runtime dependencies only
RUN apk add --no-cache dumb-init=1.2.5-r1

# Copy built application from builder stage
COPY --from=builder --chown=appuser:appgroup /app/node_modules /app/node_modules
COPY --from=builder --chown=appuser:appgroup /app/dist /app/dist
COPY --from=builder --chown=appuser:appgroup /app/package.json /app/

# Use non-root user
USER appuser

# Use dumb-init as entrypoint
ENTRYPOINT ["dumb-init", "--"]

# Set command
CMD ["node", "dist/server.js"]

# Define health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget -q -O - http://localhost:3000/health || exit 1

# Expose port
EXPOSE 3000

# Add metadata
LABEL org.opencontainers.image.source="https://github.com/example/repo" \
      org.opencontainers.image.description="Secure application image" \
      org.opencontainers.image.licenses="MIT" \
      security.selinux.type=container_runtime_t
```

### 4. Implement Kubernetes Security Controls

Configure Kubernetes security best practices for application deployment.

#### Implementation Steps:

1. Create secure namespace configuration
2. Implement Pod Security Standards
3. Configure network policies
4. Set up RBAC with least privilege
5. Implement secure secret management

#### Example Kubernetes Security Manifests:

```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: secure-app
  labels:
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted
    istio-injection: enabled

---
# network-policy.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny
  namespace: secure-app
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: api-network-policy
  namespace: secure-app
spec:
  podSelector:
    matchLabels:
      app: api
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 3000
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: database
    ports:
    - protocol: TCP
      port: 5432
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    - podSelector:
        matchLabels:
          k8s-app: kube-dns
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53

---
# rbac.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: app-service-account
  namespace: secure-app

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: app-role
  namespace: secure-app
rules:
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list"]
  resourceNames: ["app-config"]
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get"]
  resourceNames: ["app-secrets"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: app-role-binding
  namespace: secure-app
subjects:
- kind: ServiceAccount
  name: app-service-account
  namespace: secure-app
roleRef:
  kind: Role
  name: app-role
  apiGroup: rbac.authorization.k8s.io

---
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: secure-api
  namespace: secure-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: api
  template:
    metadata:
      labels:
        app: api
      annotations:
        seccomp.security.alpha.kubernetes.io/pod: runtime/default
    spec:
      serviceAccountName: app-service-account
      securityContext:
        fsGroup: 1000
        runAsNonRoot: true
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: api
        image: ${CI_REGISTRY_IMAGE}:${CI_COMMIT_SHA}
        imagePullPolicy: Always
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          privileged: false
          readOnlyRootFilesystem: true
          runAsUser: 1000
        resources:
          limits:
            cpu: "500m"
            memory: "512Mi"
          requests:
            cpu: "100m"
            memory: "128Mi"
        env:
        - name: NODE_ENV
          value: "production"
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: db_host
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: db_user
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: db_password
        volumeMounts:
        - name: tmp-volume
          mountPath: /tmp
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 10
        ports:
        - containerPort: 3000
          name: http
      volumes:
      - name: tmp-volume
        emptyDir: {}
      imagePullSecrets:
      - name: registry-credentials
```

### 5. Implement Security Monitoring and Alerting

Set up security monitoring and alerting for the application and infrastructure.

#### Implementation Steps:

1. Configure centralized logging
2. Set up security event monitoring
3. Implement alerting for security events
4. Create security dashboards
5. Configure automated response to security incidents

#### Example Security Monitoring Configuration:

```yaml
# prometheus-rules.yaml
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: security-alerts
  namespace: monitoring
spec:
  groups:
  - name: security.rules
    rules:
    # Alert on failed login attempts
    - alert: HighFailedLoginAttempts
      expr: sum(rate(app_login_failed_total[5m])) by (service) > 10
      for: 5m
      labels:
        severity: warning
        category: security
      annotations:
        summary: "High rate of failed login attempts"
        description: "Service {{ $labels.service }} has a high rate of failed login attempts ({{ $value }} per second)"
        runbook_url: "https://example.com/runbooks/high-failed-logins"

    # Alert on suspicious network activity
    - alert: UnexpectedNetworkEgress
      expr: sum(rate(container_network_transmit_bytes_total{namespace="secure-app"}[5m])) by (pod) > 1000000
      for: 15m
      labels:
        severity: warning
        category: security
      annotations:
        summary: "Unexpected high network egress"
        description: "Pod {{ $labels.pod }} in namespace secure-app has high network egress ({{ $value }} bytes/s)"
        runbook_url: "https://example.com/runbooks/unexpected-network-traffic"

    # Alert on container security issues
    - alert: PrivilegedContainer
      expr: sum(kube_pod_container_info{container!="", namespace="secure-app"} * on(pod, namespace) group_left kube_pod_container_status_running{namespace="secure-app"}) by (pod, container) > 0
      for: 5m
      labels:
        severity: critical
        category: security
      annotations:
        summary: "Privileged container detected"
        description: "Container {{ $labels.container }} in pod {{ $labels.pod }} in namespace secure-app is running with privileged permissions"
        runbook_url: "https://example.com/runbooks/privileged-container"

    # Alert on exposed secrets
    - alert: ExposedSecretDetected
      expr: sum(secret_detection_events_total) by (repository, secret_type) > 0
      for: 1m
      labels:
        severity: critical
        category: security
      annotations:
        summary: "Exposed secret detected"
        description: "Secret of type {{ $labels.secret_type }} was detected in repository {{ $labels.repository }}"
        runbook_url: "https://example.com/runbooks/exposed-secrets"

# falco-rules.yaml
customRules:
  file-integrity.yaml: |-
    - rule: Detect New File
      desc: detect new files created in sensitive directories
      condition: >
        open_write and evt.is_open_write and 
        fd.name startswith /etc and not
        (fd.name in (known_files))
      output: >
        File opened for writing in /etc (user=%user.name command=%proc.cmdline file=%fd.name)
      priority: WARNING
      tags: [filesystem]

  privilege-escalation.yaml: |-
    - rule: Privilege Escalation
      desc: detect privilege escalation via sudo
      condition: >
        spawned_process and
        proc.name = "sudo" and
        not proc.pname in (sudo, su)
      output: >
        Privilege escalation via sudo (user=%user.name command=%proc.cmdline)
      priority: WARNING
      tags: [process]

  network-anomaly.yaml: |-
    - rule: Unexpected Outbound Connection
      desc: detect unexpected outbound network connections
      condition: >
        outbound and not
        (fd.sip in (allowed_ips)) and
        not fd.sport in (allowed_ports)
      output: >
        Unexpected outbound connection (command=%proc.cmdline connection=%fd.name)
      priority: WARNING
      tags: [network]
```

### 6. Create Compliance Documentation and Evidence Collection

Implement automated compliance documentation and evidence collection.

#### Implementation Steps:

1. Define compliance requirements
2. Create compliance controls mapping
3. Implement automated evidence collection
4. Generate compliance reports
5. Set up evidence storage and retention

#### Example Compliance Documentation Script:

```python
#!/usr/bin/env python3
# compliance_documentation.py

import json
import yaml
import datetime
import os
import subprocess
import requests
import boto3
from jinja2 import Template

# Configuration
COMPLIANCE_FRAMEWORK = "PCI-DSS"
EVIDENCE_BUCKET = "compliance-evidence-bucket"
REPORT_TEMPLATE_FILE = "compliance_report_template.html"

def collect_kubernetes_evidence():
    """Collect evidence from Kubernetes cluster"""
    evidence = {}
    
    # Collect Pod Security Standards evidence
    result = subprocess.run(
        ["kubectl", "get", "ns", "-l", "pod-security.kubernetes.io/enforce=restricted", "-o", "json"],
        capture_output=True, text=True, check=True
    )
    evidence["pod_security_standards"] = json.loads(result.stdout)
    
    # Collect network policies
    result = subprocess.run(
        ["kubectl", "get", "netpol", "--all-namespaces", "-o", "json"],
        capture_output=True, text=True, check=True
    )
    evidence["network_policies"] = json.loads(result.stdout)
    
    # Collect RBAC configuration
    result = subprocess.run(
        ["kubectl", "get", "roles,rolebindings,clusterroles,clusterrolebindings", "--all-namespaces", "-o", "json"],
        capture_output=True, text=True, check=True
    )
    evidence["rbac_configuration"] = json.loads(result.stdout)
    
    return evidence

def collect_security_scan_evidence():
    """Collect evidence from security scans"""
    evidence = {}
    
    # Load container scanning results
    if os.path.exists("gl-container-scanning-report.json"):
        with open("gl-container-scanning-report.json", "r") as f:
            evidence["container_scanning"] = json.load(f)
    
    # Load dependency scanning results
    if os.path.exists("gl-dependency-scanning-report.json"):
        with open("gl-dependency-scanning-report.json", "r") as f:
            evidence["dependency_scanning"] = json.load(f)
    
    # Load SAST results
    if os.path.exists("gl-sast-report.json"):
        with open("gl-sast-report.json", "r") as f:
            evidence["sast"] = json.load(f)
    
    # Load DAST results
    if os.path.exists("gl-dast-report.json"):
        with open("gl-dast-report.json", "r") as f:
            evidence["dast"] = json.load(f)
    
    return evidence

def collect_infrastructure_evidence():
    """Collect evidence from infrastructure"""
    evidence = {}
    
    # Collect AWS Config compliance data
    if os.environ.get("AWS_ACCESS_KEY_ID"):
        config = boto3.client("config")
        compliance_details = config.describe_compliance_by_config_rule(
            ComplianceTypes=["COMPLIANT", "NON_COMPLIANT"]
        )
        evidence["aws_config"] = compliance_details
    
    # Collect CloudTrail logs summary
    if os.environ.get("AWS_ACCESS_KEY_ID"):
        cloudtrail = boto3.client("cloudtrail")
        trails = cloudtrail.describe_trails()
        evidence["cloudtrail"] = trails
    
    return evidence

def map_evidence_to_compliance_controls():
    """Map collected evidence to compliance controls"""
    # Load compliance controls mapping
    with open("compliance_controls_mapping.yaml", "r") as f:
        compliance_mapping = yaml.safe_load(f)
    
    # Collect all evidence
    evidence = {
        "kubernetes": collect_kubernetes_evidence(),
        "security_scans": collect_security_scan_evidence(),
        "infrastructure": collect_infrastructure_evidence()
    }
    
    # Map evidence to controls
    compliance_status = {}
    for control_id, control_info in compliance_mapping["controls"].items():
        compliance_status[control_id] = {
            "description": control_info["description"],
            "evidence": [],
            "status": "Unknown"
        }
        
        # Check each evidence source for this control
        for evidence_source in control_info["evidence_sources"]:
            source_type, source_name = evidence_source.split(".")
            if source_type in evidence and source_name in evidence[source_type]:
                compliance_status[control_id]["evidence"].append({
                    "source": evidence_source,
                    "data": evidence[source_type][source_name]
                })
                compliance_status[control_id]["status"] = "Compliant"
    
    return compliance_status

def generate_compliance_report(compliance_status):
    """Generate compliance report from mapped evidence"""
    # Load report template
    with open(REPORT_TEMPLATE_FILE, "r") as f:
        template_content = f.read()
    
    template = Template(template_content)
    
    # Render report
    report = template.render(
        framework=COMPLIANCE_FRAMEWORK,
        report_date=datetime.datetime.now().isoformat(),
        compliance_status=compliance_status,
        summary={
            "total_controls": len(compliance_status),
            "compliant_controls": sum(1 for c in compliance_status.values() if c["status"] == "Compliant"),
            "non_compliant_controls": sum(1 for c in compliance_status.values() if c["status"] == "Non-Compliant"),
            "unknown_controls": sum(1 for c in compliance_status.values() if c["status"] == "Unknown")
        }
    )
    
    # Save report
    with open("compliance_report.html", "w") as f:
        f.write(report)
    
    # Save evidence for audit
    if os.environ.get("AWS_ACCESS_KEY_ID"):
        s3 = boto3.client("s3")
        evidence_key = f"evidence/{datetime.datetime.now().strftime('%Y-%m-%d')}/evidence.json"
        s3.put_object(
            Bucket=EVIDENCE_BUCKET,
            Key=evidence_key,
            Body=json.dumps(compliance_status, indent=2),
            ContentType="application/json",
            Metadata={
                "compliance-framework": COMPLIANCE_FRAMEWORK,
                "report-date": datetime.datetime.now().isoformat()
            }
        )
    
    return "compliance_report.html"

if __name__ == "__main__":
    compliance_status = map_evidence_to_compliance_controls()
    report_file = generate_compliance_report(compliance_status)
    print(f"Compliance report generated: {report_file}")
```

## Project Deliverables

1. Complete DevSecOps pipeline configuration
2. Secure container image building process
3. Kubernetes security manifests
4. Security monitoring and alerting configuration
5. Compliance documentation and evidence collection system
6. Project documentation including:
   - Architecture diagram
   - Security controls documentation
   - Compliance mapping
   - Operational procedures

## Evaluation Criteria

Your implementation will be evaluated based on:

1. Comprehensive security controls across the pipeline
2. Proper implementation of security best practices
3. Automation of security testing and validation
4. Effectiveness of security monitoring and alerting
5. Quality of compliance documentation and evidence
6. Overall security posture of the deployed application

## Extension Ideas

1. Implement chaos engineering for security testing
2. Add threat modeling automation
3. Implement security policy as code with OPA
4. Create a security dashboard for real-time monitoring
5. Implement automated incident response procedures
