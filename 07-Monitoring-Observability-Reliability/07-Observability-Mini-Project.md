# Observability Mini-Project: Building a Comprehensive Observability Solution

**Skill Level: Advanced**

## Project Overview

In this mini-project, you will design and implement a comprehensive observability solution for a microservices-based e-commerce application. You will apply the concepts learned in this module to create a robust monitoring, observability, and reliability engineering setup.

## Learning Objectives

By completing this project, you will:

1. Design an end-to-end observability architecture
2. Implement metrics, logging, and tracing collection
3. Create meaningful dashboards and alerts
4. Define and implement SLOs and error budgets
5. Set up monitoring as code using infrastructure as code tools
6. Implement basic chaos engineering experiments

## Project Architecture

The e-commerce application consists of the following components:

```mermaid
graph TD
    A[Frontend] --> B[API Gateway]
    B --> C[Product Service]
    B --> D[Cart Service]
    B --> E[Order Service]
    B --> F[User Service]
    C --> G[(Product DB)]
    D --> H[(Cart DB)]
    E --> I[(Order DB)]
    F --> J[(User DB)]
    E --> K[Payment Service]
    E --> L[Notification Service]
    K --> M[Payment Gateway]
    L --> N[Email Service]
    L --> O[SMS Service]
```

## Project Tasks

### 1. Set Up Monitoring Infrastructure

Create a Kubernetes-based monitoring infrastructure using Prometheus, Grafana, Loki, and Tempo.

#### Implementation Steps:

1. Create a `monitoring` namespace in Kubernetes
2. Deploy Prometheus Operator using Helm
3. Configure ServiceMonitors for all application components
4. Deploy Grafana with Loki and Tempo data sources
5. Set up Prometheus Alertmanager with notification channels

#### Example Helm Values for Prometheus Operator:

```yaml
# prometheus-values.yaml
prometheus:
  prometheusSpec:
    replicas: 2
    retention: 15d
    serviceMonitorSelector:
      matchLabels:
        app: ecommerce
    resources:
      requests:
        memory: 2Gi
        cpu: 500m
      limits:
        memory: 4Gi
        cpu: 1000m
    storageSpec:
      volumeClaimTemplate:
        spec:
          storageClassName: standard
          resources:
            requests:
              storage: 50Gi

alertmanager:
  alertmanagerSpec:
    replicas: 2
    resources:
      requests:
        memory: 500Mi
        cpu: 100m
      limits:
        memory: 1Gi
        cpu: 200m
    storage:
      volumeClaimTemplate:
        spec:
          storageClassName: standard
          resources:
            requests:
              storage: 10Gi

grafana:
  enabled: true
  adminPassword: "changeme"
  persistence:
    enabled: true
    size: 10Gi
  additionalDataSources:
    - name: Loki
      type: loki
      url: http://loki:3100
    - name: Tempo
      type: tempo
      url: http://tempo:3100
```

### 2. Instrument Application Services

Add instrumentation to the application services to collect metrics, logs, and traces.

#### Implementation Steps:

1. Add Prometheus client libraries to each service
2. Configure structured logging with correlation IDs
3. Implement distributed tracing with OpenTelemetry
4. Add custom business metrics for key operations

#### Example Go Service Instrumentation:

```go
package main

import (
	"context"
	"log"
	"net/http"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
	"go.opentelemetry.io/otel/sdk/resource"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.4.0"
	"go.opentelemetry.io/otel/trace"
)

// Define Prometheus metrics
var (
	requestCounter = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "product_service_requests_total",
			Help: "Total number of requests to the product service",
		},
		[]string{"method", "endpoint", "status"},
	)
	
	requestDuration = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "product_service_request_duration_seconds",
			Help:    "Duration of product service requests",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"method", "endpoint"},
	)
	
	productQueries = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "product_service_queries_total",
			Help: "Total number of product queries",
		},
		[]string{"product_category", "query_type"},
	)
)

func initTracer() func() {
	// Configure exporter to send spans to OTLP endpoint
	exporter, err := otlptracegrpc.New(context.Background(),
		otlptracegrpc.WithEndpoint("otel-collector:4317"),
		otlptracegrpc.WithInsecure(),
	)
	if err != nil {
		log.Fatalf("Failed to create exporter: %v", err)
	}

	// Configure resource with service information
	res, err := resource.New(context.Background(),
		resource.WithAttributes(
			semconv.ServiceNameKey.String("product-service"),
			semconv.ServiceVersionKey.String("1.0.0"),
			attribute.String("environment", "production"),
		),
	)
	if err != nil {
		log.Fatalf("Failed to create resource: %v", err)
	}

	// Configure trace provider with the exporter
	tp := sdktrace.NewTracerProvider(
		sdktrace.WithSampler(sdktrace.AlwaysSample()),
		sdktrace.WithBatcher(exporter),
		sdktrace.WithResource(res),
	)
	otel.SetTracerProvider(tp)

	return func() {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		if err := tp.Shutdown(ctx); err != nil {
			log.Fatalf("Error shutting down tracer provider: %v", err)
		}
	}
}

func getProductHandler(w http.ResponseWriter, r *http.Request) {
	// Start timer for request duration
	start := time.Now()
	
	// Extract product ID from request
	productID := r.URL.Query().Get("id")
	if productID == "" {
		http.Error(w, "Product ID required", http.StatusBadRequest)
		requestCounter.WithLabelValues("GET", "/product", "400").Inc()
		return
	}
	
	// Get tracer
	tr := otel.Tracer("product-service")
	
	// Create a span
	ctx, span := tr.Start(r.Context(), "GetProduct",
		trace.WithAttributes(attribute.String("product.id", productID)))
	defer span.End()
	
	// Add events to the span
	span.AddEvent("Validating product ID")
	
	// Simulate database query
	time.Sleep(100 * time.Millisecond)
	
	// Create a child span for database operation
	ctx, dbSpan := tr.Start(ctx, "QueryProductDatabase")
	// Simulate database query
	time.Sleep(200 * time.Millisecond)
	dbSpan.SetAttributes(attribute.Bool("product.found", true))
	dbSpan.End()
	
	// Record business metric
	productQueries.WithLabelValues("electronics", "get_by_id").Inc()
	
	// Simulate successful response
	w.Header().Set("Content-Type", "application/json")
	w.Write([]byte(`{"id": "` + productID + `", "name": "Example Product", "price": 99.99}`))
	
	// Record request metrics
	requestCounter.WithLabelValues("GET", "/product", "200").Inc()
	requestDuration.WithLabelValues("GET", "/product").Observe(time.Since(start).Seconds())
}

func main() {
	// Initialize tracer
	cleanup := initTracer()
	defer cleanup()
	
	// Set up HTTP server
	http.HandleFunc("/product", getProductHandler)
	http.Handle("/metrics", promhttp.Handler())
	
	log.Println("Starting product service on :8080")
	log.Fatal(http.ListenAndServe(":8080", nil))
}
```

### 3. Define and Implement SLOs

Define Service Level Objectives for key services and implement monitoring for them.

#### Implementation Steps:

1. Identify critical user journeys
2. Define SLIs for availability, latency, and quality
3. Set appropriate SLO targets
4. Implement SLO monitoring with Prometheus recording rules
5. Create SLO dashboards in Grafana

#### Example SLO Definition:

```yaml
# slo-rules.yaml
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: ecommerce-slos
  namespace: monitoring
  labels:
    app: ecommerce
    role: slo-rules
spec:
  groups:
  - name: slo.rules
    rules:
    # Product Service Availability SLI
    - record: slo:product_service:availability:ratio_5m
      expr: sum(rate(product_service_requests_total{status=~"2.."}[5m])) / sum(rate(product_service_requests_total[5m]))
    
    # Product Service Latency SLI
    - record: slo:product_service:latency:ratio_5m
      expr: sum(rate(product_service_request_duration_seconds_bucket{le="0.3"}[5m])) / sum(rate(product_service_request_duration_seconds_count[5m]))
    
    # Order Service Availability SLI
    - record: slo:order_service:availability:ratio_5m
      expr: sum(rate(order_service_requests_total{status=~"2.."}[5m])) / sum(rate(order_service_requests_total[5m]))
    
    # Order Service Latency SLI
    - record: slo:order_service:latency:ratio_5m
      expr: sum(rate(order_service_request_duration_seconds_bucket{le="0.5"}[5m])) / sum(rate(order_service_request_duration_seconds_count[5m]))
    
    # Calculate error budget consumption
    - record: slo:product_service:error_budget:consumption_5m
      expr: (1 - slo:product_service:availability:ratio_5m) / (1 - 0.995)
    
    - record: slo:order_service:error_budget:consumption_5m
      expr: (1 - slo:order_service:availability:ratio_5m) / (1 - 0.99)
    
    # Alert on high error budget burn rate
    - alert: ProductServiceHighErrorBudgetBurnRate
      expr: slo:product_service:error_budget:consumption_5m > 14.4 # Will consume 100% of monthly budget in 2 days
      for: 10m
      labels:
        severity: warning
        service: product-service
      annotations:
        summary: "Product Service high error budget burn rate"
        description: "Service is consuming error budget at {{ $value }} times the sustainable rate"
```

### 4. Create Comprehensive Dashboards

Create Grafana dashboards for monitoring the application.

#### Implementation Steps:

1. Create an overview dashboard for the entire application
2. Create service-specific dashboards
3. Create SLO dashboards
4. Create business metrics dashboards
5. Implement dashboard as code using Grafonnet or Terraform

#### Example Dashboard Structure:

```mermaid
graph TD
    A[E-Commerce Overview Dashboard] --> B[Service Health Dashboard]
    A --> C[Business Metrics Dashboard]
    A --> D[SLO Dashboard]
    A --> E[User Experience Dashboard]
    
    B --> F[Product Service Dashboard]
    B --> G[Order Service Dashboard]
    B --> H[Cart Service Dashboard]
    B --> I[User Service Dashboard]
    
    C --> J[Sales Dashboard]
    C --> K[Conversion Dashboard]
    C --> L[Inventory Dashboard]
    
    D --> M[Availability SLOs]
    D --> N[Latency SLOs]
    D --> O[Error Budget Dashboard]
    
    E --> P[Page Load Times]
    E --> Q[Checkout Flow]
    E --> R[Search Performance]
```

### 5. Implement Alerting Strategy

Create a comprehensive alerting strategy for the application.

#### Implementation Steps:

1. Define alert severity levels
2. Create symptom-based alerts
3. Configure notification channels
4. Implement alert routing based on service and severity
5. Create runbooks for common alerts

#### Example Alerting Configuration:

```yaml
# alertmanager-config.yaml
global:
  resolve_timeout: 5m
  slack_api_url: '*****************************************************************************'

route:
  group_by: ['alertname', 'service']
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 4h
  receiver: 'slack-notifications'
  routes:
  - match:
      severity: critical
    receiver: 'pagerduty-critical'
    continue: true
  - match:
      severity: warning
    receiver: 'slack-notifications'
    continue: true
  - match:
      service: product-service
    receiver: 'product-team'
  - match:
      service: order-service
    receiver: 'order-team'

inhibit_rules:
- source_match:
    severity: 'critical'
  target_match:
    severity: 'warning'
  equal: ['alertname', 'service']

receivers:
- name: 'slack-notifications'
  slack_configs:
  - channel: '#alerts'
    send_resolved: true
    title: '[{{ .Status | toUpper }}] {{ .GroupLabels.alertname }}'
    text: >-
      {{ range .Alerts }}
        *Alert:* {{ .Annotations.summary }}
        *Description:* {{ .Annotations.description }}
        *Severity:* {{ .Labels.severity }}
        *Service:* {{ .Labels.service }}
      {{ end }}

- name: 'pagerduty-critical'
  pagerduty_configs:
  - service_key: '1234567890abcdef1234567890abcdef'
    send_resolved: true
    description: '{{ .GroupLabels.alertname }}'
    details:
      summary: '{{ .Annotations.summary }}'
      description: '{{ .Annotations.description }}'
      service: '{{ .Labels.service }}'

- name: 'product-team'
  slack_configs:
  - channel: '#product-team'
    send_resolved: true
    title: '[{{ .Status | toUpper }}] {{ .GroupLabels.alertname }}'
    text: >-
      {{ range .Alerts }}
        *Alert:* {{ .Annotations.summary }}
        *Description:* {{ .Annotations.description }}
        *Severity:* {{ .Labels.severity }}
      {{ end }}

- name: 'order-team'
  slack_configs:
  - channel: '#order-team'
    send_resolved: true
    title: '[{{ .Status | toUpper }}] {{ .GroupLabels.alertname }}'
    text: >-
      {{ range .Alerts }}
        *Alert:* {{ .Annotations.summary }}
        *Description:* {{ .Annotations.description }}
        *Severity:* {{ .Labels.severity }}
      {{ end }}
```

### 6. Implement Chaos Engineering Experiments

Design and implement chaos engineering experiments to test the resilience of the application.

#### Implementation Steps:

1. Define steady-state hypothesis
2. Create chaos experiments for different failure scenarios
3. Implement experiments using Chaos Mesh or Litmus Chaos
4. Monitor system behavior during experiments
5. Document findings and improvements

#### Example Chaos Experiment:

```yaml
# order-service-chaos.yaml
apiVersion: chaos-mesh.org/v1alpha1
kind: PodChaos
metadata:
  name: order-service-pod-failure
  namespace: chaos-testing
spec:
  action: pod-failure
  mode: one
  duration: "5m"
  selector:
    namespaces:
      - default
    labelSelectors:
      "app": "order-service"
  scheduler:
    cron: "@every 30m"
```

### 7. Implement Monitoring as Code

Implement the entire observability solution using infrastructure as code.

#### Implementation Steps:

1. Create Terraform modules for monitoring infrastructure
2. Define Kubernetes resources using Helm or Kustomize
3. Store dashboard definitions as code
4. Implement CI/CD pipeline for monitoring changes
5. Create documentation for the monitoring setup

#### Example Terraform Configuration:

```hcl
# monitoring.tf
module "monitoring" {
  source = "./modules/monitoring"
  
  cluster_name     = "ecommerce-cluster"
  namespace        = "monitoring"
  storage_class    = "standard"
  retention_period = "15d"
  
  grafana_admin_password = var.grafana_admin_password
  
  slack_webhook_url      = var.slack_webhook_url
  pagerduty_service_key  = var.pagerduty_service_key
  
  services = [
    {
      name = "product-service"
      port = 8080
      path = "/metrics"
      team = "product-team"
    },
    {
      name = "order-service"
      port = 8080
      path = "/metrics"
      team = "order-team"
    },
    {
      name = "cart-service"
      port = 8080
      path = "/metrics"
      team = "product-team"
    },
    {
      name = "user-service"
      port = 8080
      path = "/metrics"
      team = "user-team"
    }
  ]
  
  slos = [
    {
      service = "product-service"
      slo_name = "availability"
      target = 0.995
      window = "30d"
    },
    {
      service = "product-service"
      slo_name = "latency"
      target = 0.99
      window = "30d"
    },
    {
      service = "order-service"
      slo_name = "availability"
      target = 0.99
      window = "30d"
    },
    {
      service = "order-service"
      slo_name = "latency"
      target = 0.95
      window = "30d"
    }
  ]
}
```

## Project Deliverables

1. **Architecture Documentation**:
   - Observability architecture diagram
   - Component descriptions and interactions
   - Data flow documentation

2. **Infrastructure as Code**:
   - Terraform configurations
   - Kubernetes manifests
   - Helm charts or Kustomize configurations

3. **Dashboards and Alerts**:
   - Dashboard definitions as code
   - Alert configurations
   - Runbooks for critical alerts

4. **SLO Documentation**:
   - SLI definitions
   - SLO targets and justifications
   - Error budget policies

5. **Chaos Engineering Report**:
   - Experiment designs
   - Results and findings
   - Improvement recommendations

## Assessment Criteria

Your mini-project will be assessed based on:

1. **Architecture Design**: Completeness and appropriateness of the observability architecture
2. **Implementation Quality**: Correctness and efficiency of the implementation
3. **SLO Definition**: Appropriateness of SLIs and SLO targets
4. **Dashboard Effectiveness**: Clarity and usefulness of dashboards
5. **Alerting Strategy**: Effectiveness and noise level of alerts
6. **Code Quality**: Readability, maintainability, and modularity of code
7. **Documentation**: Clarity and completeness of documentation

## Conclusion

This mini-project provides hands-on experience with implementing a comprehensive observability solution for a microservices application. By completing this project, you will have applied the key concepts from this module in a practical context, preparing you for real-world observability challenges as a DevOps Architect.
