# Service Level Indicators, Objectives, and Agreements

**Skill Level: Intermediate**

## Notes

Service Level Indicators (SLIs), Service Level Objectives (SLOs), and Service Level Agreements (SLAs) form the foundation of reliability engineering. They provide a structured approach to defining, measuring, and improving service reliability.

### Key Concepts:

1. **Service Level Indicator (SLI)**:
   - A quantitative measure of service performance
   - Typically expressed as a ratio of "good events" to "total events"
   - Examples: availability, latency, error rate, throughput

2. **Service Level Objective (SLO)**:
   - A target value or range for an SLI
   - Represents the reliability goal for a service
   - Typically expressed as a percentage over a time window
   - Example: "99.9% of requests will complete in under 300ms over a 30-day period"

3. **Service Level Agreement (SLA)**:
   - A formal contract between service provider and customer
   - Includes SLOs with financial or legal consequences for violations
   - Typically less stringent than internal SLOs
   - Example: "Service will be available 99.5% of the time or customer receives service credits"

### SLI Categories:

1. **Availability**: Proportion of requests that are successfully handled
   - Formula: `Success Count / Total Count`
   - Example: `(200 + 300 status codes) / All Requests`

2. **Latency**: Time taken to respond to a request
   - Formula: `Requests faster than threshold / Total Requests`
   - Example: `Requests < 300ms / All Requests`

3. **Quality**: Correctness and completeness of data
   - Formula: `Correct Responses / Total Responses`
   - Example: `Valid Search Results / All Search Queries`

4. **Throughput**: Rate at which requests are processed
   - Formula: `Requests processed / Time period`
   - Example: `Transactions per second > threshold`

5. **Durability**: Likelihood of data loss
   - Formula: `Data preserved / Total data`
   - Example: `1 - (bytes lost / total bytes stored)`

### SLO Time Windows:

1. **Calendar Window**: Fixed time period (month, quarter, year)
   - Pros: Aligns with business reporting
   - Cons: Can lead to "SLO debt" at the end of the period

2. **Rolling Window**: Moving time period (last 30 days, last 90 days)
   - Pros: Provides consistent incentives
   - Cons: More complex to calculate

3. **Alerting Window**: Short-term window for detecting issues
   - Pros: Enables quick response to problems
   - Cons: May trigger false positives due to normal variance

## Practical Example: Implementing SLIs and SLOs with Prometheus

### 1. Define SLIs in Prometheus Queries

```yaml
# Availability SLI
availability_sli = sum(rate(http_requests_total{status=~"2..|3.."}[5m])) / sum(rate(http_requests_total[5m]))

# Latency SLI
latency_sli = sum(rate(http_request_duration_seconds_bucket{le="0.3"}[5m])) / sum(rate(http_request_duration_seconds_count[5m]))

# Error Budget Consumption
error_budget_consumption = (1 - availability_sli) / (1 - 0.995) # For a 99.5% SLO
```

### 2. Implement SLO Monitoring with Prometheus Operator

```yaml
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: slo-rules
  namespace: monitoring
spec:
  groups:
  - name: slo.rules
    rules:
    # Record SLIs
    - record: slo:availability:ratio_5m
      expr: sum(rate(http_requests_total{status=~"2..|3.."}[5m])) / sum(rate(http_requests_total[5m]))
    
    - record: slo:latency:ratio_5m
      expr: sum(rate(http_request_duration_seconds_bucket{le="0.3"}[5m])) / sum(rate(http_request_duration_seconds_count[5m]))
    
    # Calculate error budget consumption
    - record: slo:error_budget:consumption_5m
      expr: (1 - slo:availability:ratio_5m) / (1 - 0.995)
    
    # Alert on high error budget burn rate
    - alert: HighErrorBudgetBurnRate
      expr: slo:error_budget:consumption_5m > 14.4 # Will consume 100% of monthly budget in 2 days
      for: 10m
      labels:
        severity: warning
      annotations:
        summary: "High error budget burn rate"
        description: "Service is consuming error budget at {{ $value }} times the sustainable rate"
```

### 3. Visualize SLOs in Grafana

```mermaid
graph LR
    A[Prometheus] -->|Query SLIs| B[Grafana]
    B -->|Display| C[SLO Dashboard]
    
    subgraph "SLO Dashboard"
        D[Availability Panel]
        E[Latency Panel]
        F[Error Budget Panel]
        G[Burn Rate Panel]
    end
    
    C --> D
    C --> E
    C --> F
    C --> G
```

### 4. SLO Dashboard Configuration (Grafana JSON Model)

```json
{
  "annotations": {
    "list": []
  },
  "editable": true,
  "gnetId": null,
  "graphTooltip": 0,
  "id": 1,
  "links": [],
  "panels": [
    {
      "datasource": "Prometheus",
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "thresholds"
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "red",
                "value": null
              },
              {
                "color": "yellow",
                "value": 0.99
              },
              {
                "color": "green",
                "value": 0.995
              }
            ]
          },
          "unit": "percentunit"
        }
      },
      "gridPos": {
        "h": 8,
        "w": 12,
        "x": 0,
        "y": 0
      },
      "id": 2,
      "options": {
        "orientation": "auto",
        "reduceOptions": {
          "calcs": [
            "lastNotNull"
          ],
          "fields": "",
          "values": false
        },
        "showThresholdLabels": false,
        "showThresholdMarkers": true
      },
      "pluginVersion": "7.5.5",
      "targets": [
        {
          "expr": "slo:availability:ratio_5m",
          "interval": "",
          "legendFormat": "",
          "refId": "A"
        }
      ],
      "title": "Availability SLI (5m)",
      "type": "gauge"
    },
    {
      "datasource": "Prometheus",
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisLabel": "",
            "axisPlacement": "auto",
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 10,
            "gradientMode": "none",
            "hideFrom": {
              "legend": false,
              "tooltip": false,
              "viz": false
            },
            "lineInterpolation": "linear",
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "never",
            "spanNulls": true
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "percentunit"
        }
      },
      "gridPos": {
        "h": 8,
        "w": 12,
        "x": 12,
        "y": 0
      },
      "id": 4,
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom"
        },
        "tooltip": {
          "mode": "single"
        }
      },
      "pluginVersion": "7.5.5",
      "targets": [
        {
          "expr": "slo:availability:ratio_5m",
          "interval": "",
          "legendFormat": "Availability",
          "refId": "A"
        },
        {
          "expr": "0.995",
          "interval": "",
          "legendFormat": "SLO Target",
          "refId": "B"
        }
      ],
      "title": "Availability Over Time",
      "type": "timeseries"
    },
    {
      "datasource": "Prometheus",
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "thresholds"
          },
          "mappings": [],
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": null
              },
              {
                "color": "yellow",
                "value": 0.5
              },
              {
                "color": "red",
                "value": 0.8
              }
            ]
          },
          "unit": "percentunit"
        }
      },
      "gridPos": {
        "h": 8,
        "w": 12,
        "x": 0,
        "y": 8
      },
      "id": 6,
      "options": {
        "orientation": "auto",
        "reduceOptions": {
          "calcs": [
            "lastNotNull"
          ],
          "fields": "",
          "values": false
        },
        "showThresholdLabels": false,
        "showThresholdMarkers": true
      },
      "pluginVersion": "7.5.5",
      "targets": [
        {
          "expr": "slo:error_budget:consumption_5m",
          "interval": "",
          "legendFormat": "",
          "refId": "A"
        }
      ],
      "title": "Error Budget Consumption",
      "type": "gauge"
    }
  ],
  "refresh": "10s",
  "schemaVersion": 27,
  "style": "dark",
  "tags": [],
  "templating": {
    "list": []
  },
  "time": {
    "from": "now-6h",
    "to": "now"
  },
  "timepicker": {},
  "timezone": "",
  "title": "Service SLOs",
  "uid": "slo-dashboard",
  "version": 1
}
```

## Best Practices

1. **Choose Meaningful SLIs**:
   - Select metrics that directly reflect user experience
   - Focus on the "Four Golden Signals": latency, traffic, errors, and saturation
   - Limit to 2-5 SLIs per service to maintain focus

2. **Set Realistic SLOs**:
   - Base initial SLOs on historical performance
   - Consider business requirements and user expectations
   - Balance reliability with development velocity
   - Remember that "100% is the wrong reliability target"

3. **Implement Multi-Window SLOs**:
   - Use different time windows for different purposes
   - Short windows (1 hour) for alerting
   - Medium windows (1 day) for urgent response
   - Long windows (30 days) for reporting and planning

4. **Differentiate Between SLOs and SLAs**:
   - Set internal SLOs tighter than external SLAs
   - Create a buffer between SLOs and SLAs to allow for response time
   - Example: 99.9% SLO for a service with a 99.5% SLA

5. **Review and Revise Regularly**:
   - Evaluate SLO performance quarterly
   - Adjust targets based on business needs and technical capabilities
   - Consider seasonal patterns and growth trends

6. **Document and Communicate**:
   - Create clear documentation for SLIs and SLOs
   - Ensure all stakeholders understand the metrics
   - Report SLO performance regularly to both technical and business teams

## Common Pitfalls

1. **Measuring the Wrong Things**:
   - Focusing on system metrics rather than user experience
   - Creating SLIs that don't align with business objectives
   - Ignoring critical user journeys

2. **Setting Unrealistic Targets**:
   - Aiming for "five nines" (99.999%) without justification
   - Setting SLOs without considering technical constraints
   - Creating targets that require excessive operational burden

3. **Ignoring Implementation Complexity**:
   - Defining SLIs that are difficult to measure accurately
   - Creating complex calculations that are hard to understand
   - Failing to consider measurement overhead

4. **Treating All Services Equally**:
   - Applying the same SLOs to critical and non-critical services
   - Ignoring service dependencies in SLO definitions
   - Missing differentiation between user-facing and internal services

5. **Neglecting Error Budgets**:
   - Defining SLOs without associated error budgets
   - Failing to use error budgets to guide development decisions
   - Not having clear policies for error budget consumption

6. **Overreacting to Violations**:
   - Treating every SLO violation as an emergency
   - Failing to consider the context of violations
   - Missing the balance between reliability and innovation

## Learning Outcomes

After studying SLIs, SLOs, and SLAs, you should be able to:

1. Define appropriate Service Level Indicators for different types of services
2. Set realistic Service Level Objectives based on business needs and technical capabilities
3. Implement effective monitoring and reporting for SLO compliance
4. Differentiate between internal SLOs and external SLAs
5. Use error budgets to balance reliability and innovation
6. Create dashboards and alerts based on SLO performance
7. Develop policies for responding to SLO violations
8. Communicate SLO performance effectively to technical and business stakeholders
