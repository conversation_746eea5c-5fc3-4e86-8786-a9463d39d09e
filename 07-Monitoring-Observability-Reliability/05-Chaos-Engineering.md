# Chaos Engineering

**Skill Level: Advanced**

## Notes

Chaos Engineering is the discipline of experimenting on a system to build confidence in its capability to withstand turbulent conditions in production. It involves deliberately injecting failures into systems to test their resilience and identify weaknesses before they cause real outages.

### Key Concepts:

1. **Chaos Engineering Principles**:
   - Build a hypothesis around steady-state behavior
   - Vary real-world events (e.g., server failures, network issues)
   - Run experiments in production
   - Minimize blast radius
   - Automate experiments to run continuously

2. **Types of Chaos Experiments**:
   - **Infrastructure Chaos**: Server termination, resource exhaustion
   - **Network Chaos**: Latency, packet loss, DNS failures
   - **Application Chaos**: Process crashes, dependency failures
   - **State Chaos**: Database corruption, cache invalidation
   - **People Chaos**: On-call simulations, game days

3. **Chaos Maturity Model**:
   - **Level 1**: Manual experiments in non-production
   - **Level 2**: Automated experiments in non-production
   - **Level 3**: Manual experiments in production
   - **Level 4**: Automated experiments in production
   - **Level 5**: Continuous chaos in production

4. **Chaos Engineering Process**:
   - Define steady state and metrics
   - Form a hypothesis
   - Design the experiment
   - Run the experiment
   - Analyze results
   - Improve the system
   - Repeat

### Benefits of Chaos Engineering:

1. **Improved Resilience**: Identify and fix weaknesses before they cause outages
2. **Reduced MTTR**: Practice incident response in a controlled environment
3. **Increased Confidence**: Build trust in system capabilities
4. **Better Understanding**: Improve knowledge of system behavior
5. **Cultural Shift**: Move from reactive to proactive reliability

## Practical Example: Implementing Chaos Engineering

### 1. Chaos Experiment Design Document

```markdown
# Chaos Experiment: API Service Resilience Test

## Experiment Overview
This experiment tests the resilience of our API service when downstream dependencies fail.

## Steady State Hypothesis
The API service will maintain 99.9% availability and p95 latency under 300ms even when the user database experiences intermittent failures.

## Method
1. Measure baseline metrics for 10 minutes
2. Inject 30% failure rate into database connections for 15 minutes
3. Observe system behavior
4. Restore normal operation
5. Continue monitoring for 10 minutes

## Metrics to Monitor
- API availability (success rate)
- API latency (p50, p95, p99)
- Error rates by type
- Cache hit/miss ratio
- Circuit breaker state

## Blast Radius Limitation
- Experiment will only affect 10% of production traffic
- Automatic abort if availability drops below 95%
- Manual abort button available to experiment operator
- Pre-notification to on-call team

## Success Criteria
- Availability remains above 99.5% during the experiment
- p95 latency increases by no more than 100ms
- All affected requests receive graceful degradation
- No unexpected alerts triggered

## Rollback Plan
1. Stop the chaos injection
2. Restart affected services if necessary
3. Clear any corrupted cache entries
4. Notify users if any data inconsistency occurred
```

### 2. Chaos Experiment Implementation with Chaos Toolkit

```yaml
# chaos-database-experiment.yaml
apiVersion: chaos-toolkit/v1
kind: Experiment
title: API resilience when database fails
description: Verify that the API service remains available when the database experiences failures
tags:
  - database
  - api
  - resilience

steady-state-hypothesis:
  title: API is available and responsive
  probes:
    - name: api-health-check
      type: probe
      tolerance: 200
      provider:
        type: http
        url: https://api.example.com/health
        method: GET
        timeout: 3
    - name: api-success-rate
      type: probe
      tolerance: [99, 100]
      provider:
        type: python
        module: chaosprometheus.probes
        func: query_instant
        arguments:
          query: sum(rate(http_requests_total{status=~"2.."}[1m])) / sum(rate(http_requests_total[1m])) * 100
          endpoint: https://prometheus.example.com

method:
  - name: enable-database-fault-injection
    type: action
    provider:
      type: python
      module: chaosaws.rds.actions
      func: failover_db_cluster
      arguments:
        cluster_identifier: "user-db-cluster"
        target_db_instance_identifier: "user-db-instance-2"
    pauses:
      after: 300

  - name: verify-api-degraded-operation
    type: probe
    tolerance: 200
    provider:
      type: http
      url: https://api.example.com/users
      method: GET
      timeout: 5

rollbacks:
  - name: restart-api-pods
    type: action
    provider:
      type: python
      module: chaosk8s.pod.actions
      func: delete_pods
      arguments:
        label_selector: "app=api-service"
        ns: production
        grace_period: 30
```

### 3. Chaos Engineering Platform Architecture

```mermaid
graph TD
    A[Chaos Platform] --> B[Experiment Scheduler]
    A --> C[Experiment Repository]
    A --> D[Results Database]
    A --> E[Monitoring Integration]
    
    B --> F[Experiment Runner]
    F --> G[Fault Injectors]
    
    G --> H[Infrastructure Faults]
    G --> I[Network Faults]
    G --> J[Application Faults]
    
    E --> K[Metrics Collection]
    E --> L[Log Analysis]
    E --> M[Tracing Integration]
    
    F --> N[Safety Mechanisms]
    N --> O[Automatic Rollback]
    N --> P[Blast Radius Control]
    
    subgraph "Target Systems"
        Q[Production Services]
        R[Staging Environment]
        S[Development Environment]
    end
    
    G --> Q
    G --> R
    G --> S
    
    Q --> K
    R --> K
    S --> K
```

### 4. Chaos Engineering with Kubernetes and Litmus

```yaml
# chaos-experiment.yaml
apiVersion: litmuschaos.io/v1alpha1
kind: ChaosEngine
metadata:
  name: api-service-chaos
  namespace: default
spec:
  appinfo:
    appns: 'default'
    applabel: 'app=api-service'
    appkind: 'deployment'
  chaosServiceAccount: litmus-admin
  monitoring: true
  jobCleanUpPolicy: 'delete'
  annotationCheck: 'true'
  engineState: 'active'
  auxiliaryAppInfo: ''
  chaosType: 'infra'  # infra, application
  components:
    runner:
      image: 'litmuschaos/chaos-runner:latest'
      type: 'go'
  experiments:
    - name: pod-delete
      spec:
        components:
          env:
            - name: TOTAL_CHAOS_DURATION
              value: '60'
            - name: CHAOS_INTERVAL
              value: '10'
            - name: FORCE
              value: 'false'
            - name: PODS_AFFECTED_PERC
              value: '25'
    - name: container-kill
      spec:
        components:
          env:
            - name: TOTAL_CHAOS_DURATION
              value: '60'
            - name: CHAOS_INTERVAL
              value: '10'
            - name: CONTAINER_RUNTIME
              value: 'containerd'
            - name: SOCKET_PATH
              value: '/var/run/containerd/containerd.sock'
    - name: pod-network-latency
      spec:
        components:
          env:
            - name: TOTAL_CHAOS_DURATION
              value: '60'
            - name: NETWORK_LATENCY
              value: '2000'
            - name: CONTAINER_RUNTIME
              value: 'containerd'
            - name: SOCKET_PATH
              value: '/var/run/containerd/containerd.sock'
```

## Best Practices

1. **Start Small and Expand**:
   - Begin with simple experiments in non-production
   - Gradually increase complexity and scope
   - Build confidence before running in production
   - Expand to more critical systems as maturity increases

2. **Define Clear Hypotheses**:
   - Create specific, measurable hypotheses
   - Focus on business-relevant metrics
   - Document expected behavior before experiments
   - Validate or invalidate hypotheses explicitly

3. **Minimize Blast Radius**:
   - Limit the scope of initial experiments
   - Use canary or segmented approaches
   - Implement automatic experiment termination
   - Have clear rollback procedures

4. **Integrate with Observability**:
   - Ensure comprehensive monitoring before chaos testing
   - Collect detailed metrics during experiments
   - Correlate chaos events with system behavior
   - Use tracing to understand failure propagation

5. **Build a Chaos Engineering Culture**:
   - Get leadership buy-in
   - Educate teams on the value of controlled failure
   - Celebrate learning from experiments
   - Share results and improvements widely

6. **Automate Chaos Experiments**:
   - Create repeatable, automated experiments
   - Run experiments on a regular schedule
   - Integrate with CI/CD pipelines
   - Consider chaos as part of testing strategy

## Common Pitfalls

1. **Inadequate Monitoring**:
   - Running experiments without proper observability
   - Missing key metrics to evaluate impact
   - Inability to determine experiment success or failure
   - Lack of historical data for comparison

2. **Excessive Blast Radius**:
   - Affecting too many systems or users
   - Missing safeguards to limit damage
   - Inadequate rollback mechanisms
   - Causing unintended consequences

3. **Poor Timing**:
   - Running experiments during peak business hours
   - Conflicting with other major activities
   - Inadequate notification to stakeholders
   - Missing coordination with on-call teams

4. **Unclear Hypotheses**:
   - Running experiments without specific expectations
   - Failing to document baseline behavior
   - Missing success criteria
   - Inability to learn from results

5. **Neglecting Security**:
   - Creating new attack vectors through chaos tools
   - Insufficient access controls for chaos platforms
   - Exposing sensitive information during experiments
   - Missing security validation of chaos tools

6. **Ignoring Results**:
   - Failing to fix issues discovered by experiments
   - Not sharing findings with relevant teams
   - Missing follow-up experiments to validate fixes
   - Treating chaos engineering as a checkbox exercise

## Learning Outcomes

After studying chaos engineering, you should be able to:

1. Design and implement chaos experiments with clear hypotheses
2. Select appropriate chaos engineering tools for different environments
3. Integrate chaos engineering with existing monitoring and observability systems
4. Implement safety mechanisms to limit the impact of experiments
5. Build a chaos engineering practice within an organization
6. Use chaos engineering to improve system resilience
7. Develop a culture that embraces controlled failure as a learning opportunity
8. Apply chaos engineering principles to different types of systems and architectures
