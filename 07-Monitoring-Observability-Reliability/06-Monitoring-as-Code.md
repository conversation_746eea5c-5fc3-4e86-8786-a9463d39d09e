# Monitoring as Code

**Skill Level: Advanced**

## Notes

Monitoring as Code (MaC) is the practice of defining, deploying, and managing monitoring infrastructure, dashboards, and alerts using code and automation. This approach brings the benefits of Infrastructure as Code (IaC) to the monitoring domain, enabling version control, reproducibility, and consistency.

### Key Concepts:

1. **Monitoring as Code Principles**:
   - Define monitoring resources in code
   - Version control monitoring configurations
   - Automate deployment of monitoring resources
   - Test monitoring configurations
   - Apply CI/CD practices to monitoring

2. **Components of Monitoring as Code**:
   - **Monitoring Infrastructure**: Servers, databases, and collectors
   - **Data Sources**: Metrics, logs, and traces collection
   - **Dashboards**: Visualization of monitoring data
   - **Alerts**: Conditions and notification channels
   - **Recording Rules**: Pre-computed expressions
   - **Service Level Objectives**: SLIs and SLOs

3. **Benefits of Monitoring as Code**:
   - **Consistency**: Standardized monitoring across environments
   - **Reproducibility**: Easily recreate monitoring setups
   - **Scalability**: Efficiently manage monitoring at scale
   - **Auditability**: Track changes to monitoring configurations
   - **Collaboration**: Enable team contributions to monitoring
   - **Testing**: Validate monitoring before deployment

4. **Implementation Approaches**:
   - **Declarative**: Define desired state (Terraform, Kubernetes)
   - **Imperative**: Define steps to create resources (Scripts)
   - **Hybrid**: Combine declarative and imperative approaches

### Tools for Monitoring as Code:

1. **Infrastructure Provisioning**:
   - Terraform, Pulumi, CloudFormation
   - Kubernetes Operators (Prometheus Operator)

2. **Configuration Management**:
   - Ansible, Chef, Puppet
   - Jsonnet, Helm

3. **Dashboard as Code**:
   - Grafana Dashboards JSON
   - Grafonnet (Jsonnet for Grafana)
   - Terraform Grafana Provider

4. **Alerting as Code**:
   - Prometheus Alertmanager configurations
   - PagerDuty API and Terraform provider
   - OpsGenie Terraform provider

## Practical Example: Monitoring as Code with Terraform and Kubernetes

### 1. Prometheus and Grafana Infrastructure with Terraform

```hcl
# main.tf - Terraform configuration for monitoring infrastructure

provider "aws" {
  region = "us-west-2"
}

# Create VPC for monitoring infrastructure
module "vpc" {
  source = "terraform-aws-modules/vpc/aws"
  
  name = "monitoring-vpc"
  cidr = "10.0.0.0/16"
  
  azs             = ["us-west-2a", "us-west-2b"]
  private_subnets = ["10.0.1.0/24", "10.0.2.0/24"]
  public_subnets  = ["10.0.101.0/24", "10.0.102.0/24"]
  
  enable_nat_gateway = true
  single_nat_gateway = true
  
  tags = {
    Environment = "production"
    Purpose     = "monitoring"
  }
}

# Create EKS cluster for monitoring
module "eks" {
  source = "terraform-aws-modules/eks/aws"
  
  cluster_name    = "monitoring-cluster"
  cluster_version = "1.24"
  
  vpc_id          = module.vpc.vpc_id
  subnet_ids      = module.vpc.private_subnets
  
  eks_managed_node_groups = {
    monitoring = {
      desired_capacity = 3
      max_capacity     = 5
      min_capacity     = 2
      
      instance_types = ["m5.large"]
      
      labels = {
        role = "monitoring"
      }
    }
  }
  
  tags = {
    Environment = "production"
    Purpose     = "monitoring"
  }
}

# Create S3 bucket for long-term metrics storage
resource "aws_s3_bucket" "prometheus_thanos" {
  bucket = "example-prometheus-thanos-storage"
  
  lifecycle_rule {
    id      = "metrics-retention"
    enabled = true
    
    transition {
      days          = 30
      storage_class = "STANDARD_IA"
    }
    
    transition {
      days          = 90
      storage_class = "GLACIER"
    }
    
    expiration {
      days = 365
    }
  }
  
  tags = {
    Name        = "Prometheus Thanos Storage"
    Environment = "production"
  }
}

# Create IAM policy for Prometheus to access S3
resource "aws_iam_policy" "prometheus_s3_access" {
  name        = "prometheus-s3-access"
  description = "Allow Prometheus to access S3 for long-term storage"
  
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "s3:ListBucket",
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject"
        ]
        Effect   = "Allow"
        Resource = [
          aws_s3_bucket.prometheus_thanos.arn,
          "${aws_s3_bucket.prometheus_thanos.arn}/*"
        ]
      }
    ]
  })
}

# Output the kubeconfig command
output "kubeconfig_command" {
  value = "aws eks update-kubeconfig --region us-west-2 --name monitoring-cluster"
}
```

### 2. Prometheus Operator with Kubernetes Custom Resources

```yaml
# prometheus-operator.yaml - Kubernetes manifest for Prometheus Operator

apiVersion: monitoring.coreos.com/v1
kind: Prometheus
metadata:
  name: prometheus
  namespace: monitoring
spec:
  replicas: 2
  version: v2.40.0
  serviceAccountName: prometheus
  securityContext:
    fsGroup: 2000
    runAsNonRoot: true
    runAsUser: 1000
  serviceMonitorSelector:
    matchLabels:
      team: platform
  ruleSelector:
    matchLabels:
      role: alert-rules
      team: platform
  alerting:
    alertmanagers:
    - namespace: monitoring
      name: alertmanager
      port: web
  storage:
    volumeClaimTemplate:
      spec:
        storageClassName: gp2
        resources:
          requests:
            storage: 100Gi
  retention: 15d
  thanos:
    baseImage: quay.io/thanos/thanos
    version: v0.28.0
    objectStorageConfig:
      name: thanos-objstore-config
      key: config.yaml

---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: api-service-monitor
  namespace: monitoring
  labels:
    team: platform
spec:
  selector:
    matchLabels:
      app: api-service
  namespaceSelector:
    matchNames:
      - default
      - production
  endpoints:
  - port: metrics
    interval: 15s
    path: /metrics
    scrapeTimeout: 10s
    metricRelabelings:
    - sourceLabels: [__name__]
      regex: 'go_.*'
      action: drop

---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: api-service-alerts
  namespace: monitoring
  labels:
    role: alert-rules
    team: platform
spec:
  groups:
  - name: api.rules
    rules:
    - alert: APIHighErrorRate
      expr: sum(rate(http_requests_total{status=~"5.."}[5m])) / sum(rate(http_requests_total[5m])) > 0.05
      for: 5m
      labels:
        severity: critical
        team: api
      annotations:
        summary: "High API error rate"
        description: "API error rate is {{ $value | humanizePercentage }} for the last 5 minutes"
    - alert: APIHighLatency
      expr: histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le)) > 0.5
      for: 5m
      labels:
        severity: warning
        team: api
      annotations:
        summary: "High API latency"
        description: "API p95 latency is {{ $value | humanizeDuration }} for the last 5 minutes"
```

### 3. Grafana Dashboards as Code with Grafonnet (Jsonnet)

```jsonnet
// api_dashboard.jsonnet - Grafana dashboard defined with Grafonnet

local grafana = import 'grafonnet/grafana.libsonnet';
local dashboard = grafana.dashboard;
local row = grafana.row;
local prometheus = grafana.prometheus;
local template = grafana.template;
local graphPanel = grafana.graphPanel;
local statPanel = grafana.statPanel;

local datasource = 'Prometheus';

dashboard.new(
  'API Service Dashboard',
  tags=['api', 'service'],
  time_from='now-6h',
  refresh='1m',
  uid='api-service-dashboard',
)
.addTemplate(
  template.new(
    'namespace',
    datasource,
    'label_values(http_requests_total, namespace)',
    label='Namespace',
    refresh='time',
  )
)
.addTemplate(
  template.new(
    'service',
    datasource,
    'label_values(http_requests_total{namespace="$namespace"}, service)',
    label='Service',
    refresh='time',
  )
)
.addRow(
  row.new(
    title='Request Overview'
  )
  .addPanel(
    graphPanel.new(
      'Request Rate',
      datasource=datasource,
      description='HTTP requests per second',
      format='ops',
      min=0,
    )
    .addTarget(
      prometheus.target(
        'sum(rate(http_requests_total{namespace="$namespace", service="$service"}[5m])) by (status_code)',
        legendFormat='{{status_code}}',
      )
    ),
    gridPos={ h: 8, w: 12, x: 0, y: 0 }
  )
  .addPanel(
    graphPanel.new(
      'Error Rate',
      datasource=datasource,
      description='Percentage of 5xx responses',
      format='percentunit',
      min=0,
      max=1,
    )
    .addTarget(
      prometheus.target(
        'sum(rate(http_requests_total{namespace="$namespace", service="$service", status_code=~"5.."}[5m])) / sum(rate(http_requests_total{namespace="$namespace", service="$service"}[5m]))',
        legendFormat='Error Rate',
      )
    ),
    gridPos={ h: 8, w: 12, x: 12, y: 0 }
  )
)
.addRow(
  row.new(
    title='Latency'
  )
  .addPanel(
    graphPanel.new(
      'Latency Percentiles',
      datasource=datasource,
      description='Request duration by percentile',
      format='s',
      min=0,
    )
    .addTarget(
      prometheus.target(
        'histogram_quantile(0.50, sum(rate(http_request_duration_seconds_bucket{namespace="$namespace", service="$service"}[5m])) by (le))',
        legendFormat='p50',
      )
    )
    .addTarget(
      prometheus.target(
        'histogram_quantile(0.90, sum(rate(http_request_duration_seconds_bucket{namespace="$namespace", service="$service"}[5m])) by (le))',
        legendFormat='p90',
      )
    )
    .addTarget(
      prometheus.target(
        'histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{namespace="$namespace", service="$service"}[5m])) by (le))',
        legendFormat='p95',
      )
    )
    .addTarget(
      prometheus.target(
        'histogram_quantile(0.99, sum(rate(http_request_duration_seconds_bucket{namespace="$namespace", service="$service"}[5m])) by (le))',
        legendFormat='p99',
      )
    ),
    gridPos={ h: 8, w: 24, x: 0, y: 8 }
  )
)
.addRow(
  row.new(
    title='SLO Status'
  )
  .addPanel(
    statPanel.new(
      'Availability SLO',
      datasource=datasource,
      description='30-day availability against 99.9% SLO',
      unit='percentunit',
      decimals=4,
      colorMode='background',
      thresholds=[
        { color: 'red', value: 0 },
        { color: 'yellow', value: 0.999 },
        { color: 'green', value: 0.9995 },
      ],
    )
    .addTarget(
      prometheus.target(
        'sum(rate(http_requests_total{namespace="$namespace", service="$service", status_code!~"5.."}[30d])) / sum(rate(http_requests_total{namespace="$namespace", service="$service"}[30d]))',
      )
    ),
    gridPos={ h: 8, w: 8, x: 0, y: 16 }
  )
  .addPanel(
    statPanel.new(
      'Latency SLO',
      datasource=datasource,
      description='30-day latency against 300ms SLO',
      unit='percentunit',
      decimals=4,
      colorMode='background',
      thresholds=[
        { color: 'red', value: 0 },
        { color: 'yellow', value: 0.95 },
        { color: 'green', value: 0.98 },
      ],
    )
    .addTarget(
      prometheus.target(
        'sum(rate(http_request_duration_seconds_bucket{namespace="$namespace", service="$service", le="0.3"}[30d])) / sum(rate(http_request_duration_seconds_count{namespace="$namespace", service="$service"}[30d]))',
      )
    ),
    gridPos={ h: 8, w: 8, x: 8, y: 16 }
  )
  .addPanel(
    statPanel.new(
      'Error Budget Remaining',
      datasource=datasource,
      description='Remaining error budget for the month',
      unit='percentunit',
      decimals=2,
      colorMode='background',
      thresholds=[
        { color: 'red', value: 0 },
        { color: 'yellow', value: 0.5 },
        { color: 'green', value: 0.8 },
      ],
    )
    .addTarget(
      prometheus.target(
        '1 - (1 - (sum(rate(http_requests_total{namespace="$namespace", service="$service", status_code!~"5.."}[30d])) / sum(rate(http_requests_total{namespace="$namespace", service="$service"}[30d])))) / (1 - 0.999)',
      )
    ),
    gridPos={ h: 8, w: 8, x: 16, y: 16 }
  )
)
```

### 4. Alerting as Code with Terraform and PagerDuty

```hcl
# alerting.tf - Terraform configuration for alerting

provider "pagerduty" {
  token = var.pagerduty_token
}

# Create PagerDuty service for API alerts
resource "pagerduty_service" "api_service" {
  name              = "API Service"
  description       = "API Service alerts"
  escalation_policy = pagerduty_escalation_policy.api_team.id
  alert_creation    = "create_alerts_and_incidents"
  
  incident_urgency_rule {
    type    = "constant"
    urgency = "high"
  }
  
  auto_resolve_timeout = 14400 # 4 hours
}

# Create escalation policy
resource "pagerduty_escalation_policy" "api_team" {
  name      = "API Team Escalation Policy"
  num_loops = 3
  
  rule {
    escalation_delay_in_minutes = 15
    
    target {
      type = "user_reference"
      id   = pagerduty_user.primary_oncall.id
    }
  }
  
  rule {
    escalation_delay_in_minutes = 15
    
    target {
      type = "user_reference"
      id   = pagerduty_user.secondary_oncall.id
    }
  }
  
  rule {
    escalation_delay_in_minutes = 30
    
    target {
      type = "user_reference"
      id   = pagerduty_user.engineering_manager.id
    }
  }
}

# Create integration for Alertmanager
resource "pagerduty_service_integration" "prometheus" {
  name    = "Prometheus Alertmanager"
  service = pagerduty_service.api_service.id
  type    = "events_api_v2_inbound_integration"
}

# Output the integration key for Alertmanager configuration
output "pagerduty_integration_key" {
  value     = pagerduty_service_integration.prometheus.integration_key
  sensitive = true
}

# Create Alertmanager configuration as a Kubernetes Secret
resource "kubernetes_secret" "alertmanager_config" {
  metadata {
    name      = "alertmanager-config"
    namespace = "monitoring"
  }
  
  data = {
    "alertmanager.yaml" = yamlencode({
      global = {
        resolve_timeout = "5m"
      }
      
      route = {
        group_by        = ["alertname", "job"]
        group_wait      = "30s"
        group_interval  = "5m"
        repeat_interval = "12h"
        receiver        = "default"
        routes = [
          {
            match = {
              team = "api"
            }
            receiver = "api-team"
          }
        ]
      }
      
      receivers = [
        {
          name = "default"
          email_configs = [
            {
              to           = "<EMAIL>"
              send_resolved = true
            }
          ]
        },
        {
          name = "api-team"
          pagerduty_configs = [
            {
              service_key  = pagerduty_service_integration.prometheus.integration_key
              send_resolved = true
            }
          ]
        }
      ]
    })
  }
}
```

## Best Practices

1. **Standardize Monitoring Templates**:
   - Create reusable templates for common monitoring patterns
   - Establish naming conventions for metrics and dashboards
   - Define standard labels and annotations
   - Create a monitoring service catalog

2. **Implement Modular Design**:
   - Separate infrastructure, dashboards, and alerts
   - Use modules or packages for reusable components
   - Create hierarchical configurations
   - Enable composition of monitoring resources

3. **Version and Test Monitoring Code**:
   - Store monitoring code in version control
   - Implement CI/CD pipelines for monitoring
   - Validate configurations before deployment
   - Test monitoring in non-production environments

4. **Document Monitoring Resources**:
   - Include descriptions in monitoring code
   - Document alert rationale and response procedures
   - Create runbooks for common alerts
   - Maintain a monitoring architecture diagram

5. **Implement Governance**:
   - Define ownership for monitoring resources
   - Establish review processes for monitoring changes
   - Create standards for alert severity and notification channels
   - Regularly audit and clean up monitoring resources

6. **Balance Flexibility and Standardization**:
   - Allow teams to create custom monitoring
   - Provide guardrails and best practices
   - Enforce critical monitoring standards
   - Enable self-service within boundaries

## Common Pitfalls

1. **Excessive Complexity**:
   - Creating overly complex monitoring configurations
   - Using advanced features without clear benefits
   - Implementing custom solutions for standard problems
   - Missing documentation for complex setups

2. **Inadequate Testing**:
   - Deploying monitoring changes without testing
   - Missing validation of alert conditions
   - Failing to test notification channels
   - Not verifying dashboard queries

3. **Configuration Drift**:
   - Manual changes to monitoring resources
   - Inconsistency between code and deployed state
   - Missing enforcement of configuration as code
   - Allowing direct access to monitoring platforms

4. **Poor Scalability**:
   - Hardcoding values that should be parameterized
   - Missing templating for repeated configurations
   - Creating one-off monitoring resources
   - Failing to consider growth in monitoring needs

5. **Neglecting Maintenance**:
   - Accumulating unused monitoring resources
   - Missing updates to monitoring tools
   - Failing to review and adjust alerts
   - Not adapting monitoring to changing systems

6. **Siloed Monitoring**:
   - Creating team-specific monitoring solutions
   - Missing integration between monitoring systems
   - Duplicating monitoring efforts
   - Inconsistent monitoring practices across teams

## Learning Outcomes

After studying monitoring as code, you should be able to:

1. Implement monitoring infrastructure using infrastructure as code tools
2. Create and manage dashboards and alerts using code
3. Design modular and reusable monitoring configurations
4. Implement CI/CD pipelines for monitoring resources
5. Apply version control best practices to monitoring code
6. Balance standardization and flexibility in monitoring
7. Integrate monitoring as code with broader DevOps practices
8. Scale monitoring efficiently across multiple services and environments
