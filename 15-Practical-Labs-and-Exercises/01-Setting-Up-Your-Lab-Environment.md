# Setting Up Your Lab Environment

**Skill Level: Beginner**

## Notes

A personal lab environment is essential for DevOps Architects to experiment with technologies, test architectural patterns, and develop practical skills. This guide will help you set up a flexible, cost-effective lab environment that can be used for the hands-on exercises throughout this study plan.

### Lab Environment Requirements

An effective DevOps lab environment should:

1. **Be Reproducible**: Easy to recreate if needed
2. **Be Cost-Effective**: Minimize expenses while providing necessary capabilities
3. **Support Multiple Technologies**: Accommodate various tools and platforms
4. **Be Isolated**: Avoid impacting production environments
5. **Be Scalable**: Able to grow as your learning needs expand
6. **Support Infrastructure as Code**: Allow for declarative configuration

### Lab Environment Options

#### Option 1: Local Development Environment
- **Description**: Run everything on your local machine using virtualization
- **Pros**: No ongoing costs, works offline, full control
- **Cons**: Limited by your hardware, can be resource-intensive
- **Best for**: Initial learning, small-scale experiments, limited budget

#### Option 2: Cloud-Based Lab
- **Description**: Use cloud provider resources for your lab
- **Pros**: Scalable, realistic, access to managed services
- **Cons**: Ongoing costs, requires internet connection
- **Best for**: Production-like scenarios, scaling experiments, cloud-specific learning

#### Option 3: Hybrid Approach
- **Description**: Combine local resources with cloud services
- **Pros**: Flexibility, cost control, realistic integrations
- **Cons**: More complex setup, potential networking challenges
- **Best for**: Balanced learning, multi-environment testing, most DevOps Architects

## Practical Example: Setting Up a Hybrid Lab Environment

### 1. Local Environment Setup

```bash
# Install Multipass for lightweight VM management
# macOS
brew install --cask multipass

# Windows
# Download from https://multipass.run/download/windows

# Launch a VM with 2 CPUs, 4GB RAM, and 20GB disk
multipass launch --name devops-lab --cpus 2 --mem 4G --disk 20G

# Access the VM shell
multipass shell devops-lab

# Install Docker
sudo apt update
sudo apt install -y apt-transport-https ca-certificates curl software-properties-common
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -
sudo add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable"
sudo apt update
sudo apt install -y docker-ce
sudo usermod -aG docker $USER

# Install Minikube for local Kubernetes
curl -LO https://storage.googleapis.com/minikube/releases/latest/minikube-linux-amd64
sudo install minikube-linux-amd64 /usr/local/bin/minikube

# Install kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl

# Start Minikube
minikube start --driver=docker --cpus=2 --memory=3g

# Verify installation
kubectl get nodes
```

### 2. Cloud Environment Setup with Terraform

```hcl
# main.tf - AWS Example

provider "aws" {
  region = "us-west-2"
}

# Create a VPC for lab isolation
resource "aws_vpc" "lab_vpc" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_support   = true
  enable_dns_hostnames = true
  
  tags = {
    Name = "devops-lab-vpc"
    Environment = "lab"
  }
}

# Create a public subnet
resource "aws_subnet" "public_subnet" {
  vpc_id                  = aws_vpc.lab_vpc.id
  cidr_block              = "********/24"
  map_public_ip_on_launch = true
  availability_zone       = "us-west-2a"
  
  tags = {
    Name = "devops-lab-public-subnet"
    Environment = "lab"
  }
}

# Create an internet gateway
resource "aws_internet_gateway" "lab_igw" {
  vpc_id = aws_vpc.lab_vpc.id
  
  tags = {
    Name = "devops-lab-igw"
    Environment = "lab"
  }
}

# Create a route table
resource "aws_route_table" "public_rt" {
  vpc_id = aws_vpc.lab_vpc.id
  
  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.lab_igw.id
  }
  
  tags = {
    Name = "devops-lab-public-rt"
    Environment = "lab"
  }
}

# Associate route table with subnet
resource "aws_route_table_association" "public_rta" {
  subnet_id      = aws_subnet.public_subnet.id
  route_table_id = aws_route_table.public_rt.id
}

# Create a security group
resource "aws_security_group" "lab_sg" {
  name        = "devops-lab-sg"
  description = "Allow SSH and internal traffic"
  vpc_id      = aws_vpc.lab_vpc.id
  
  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["YOUR_IP_ADDRESS/32"]  # Replace with your IP
  }
  
  ingress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["10.0.0.0/16"]
  }
  
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
  
  tags = {
    Name = "devops-lab-sg"
    Environment = "lab"
  }
}

# Create an EC2 instance
resource "aws_instance" "lab_server" {
  ami                    = "ami-0c55b159cbfafe1f0"  # Ubuntu 20.04 LTS
  instance_type          = "t3.medium"
  key_name               = "your-key-pair"  # Replace with your key pair
  subnet_id              = aws_subnet.public_subnet.id
  vpc_security_group_ids = [aws_security_group.lab_sg.id]
  
  root_block_device {
    volume_size = 30
    volume_type = "gp3"
  }
  
  tags = {
    Name = "devops-lab-server"
    Environment = "lab"
  }
}

output "server_public_ip" {
  value = aws_instance.lab_server.public_ip
}
```

### 3. Setting Up Local-to-Cloud Connectivity

```bash
# On your local machine, create an SSH config for easy access
cat << EOF >> ~/.ssh/config
Host lab-server
  HostName <EC2_PUBLIC_IP>
  User ubuntu
  IdentityFile ~/.ssh/your-key-pair.pem
  StrictHostKeyChecking no
EOF

# Test the connection
ssh lab-server

# Set up kubectl to work with both local and cloud environments
# For local Minikube
kubectl config use-context minikube

# For AWS EKS (if you set up a Kubernetes cluster in AWS)
aws eks update-kubeconfig --name your-eks-cluster --region us-west-2

# Create a script to switch contexts easily
cat << 'EOF' > ~/switch-k8s.sh
#!/bin/bash
if [ "$1" == "local" ]; then
  kubectl config use-context minikube
  echo "Switched to local Kubernetes cluster"
elif [ "$1" == "cloud" ]; then
  kubectl config use-context <your-eks-context>
  echo "Switched to cloud Kubernetes cluster"
else
  echo "Usage: switch-k8s.sh [local|cloud]"
fi
EOF

chmod +x ~/switch-k8s.sh
```

### 4. Installing Essential DevOps Tools

```bash
# Install Terraform
curl -fsSL https://apt.releases.hashicorp.com/gpg | sudo apt-key add -
sudo apt-add-repository "deb [arch=amd64] https://apt.releases.hashicorp.com $(lsb_release -cs) main"
sudo apt update
sudo apt install terraform

# Install AWS CLI
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install

# Install Helm
curl https://baltocdn.com/helm/signing.asc | sudo apt-key add -
sudo apt-get install apt-transport-https --yes
echo "deb https://baltocdn.com/helm/stable/debian/ all main" | sudo tee /etc/apt/sources.list.d/helm-stable-debian.list
sudo apt-get update
sudo apt-get install helm

# Install k9s for Kubernetes management
curl -sS https://webinstall.dev/k9s | bash

# Install Ansible
sudo apt update
sudo apt install -y ansible

# Install Prometheus and Grafana using Helm
kubectl create namespace monitoring
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo update
helm install prometheus prometheus-community/kube-prometheus-stack --namespace monitoring
```

### 5. Setting Up a GitOps Workflow

```bash
# Install Flux CD
curl -s https://fluxcd.io/install.sh | sudo bash

# Create a Git repository for your infrastructure
mkdir -p ~/gitops/infrastructure
cd ~/gitops/infrastructure
git init

# Create a basic structure
mkdir -p {base,environments/{dev,staging,prod}}

# Create a sample application deployment
cat << EOF > base/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sample-app
spec:
  replicas: 2
  selector:
    matchLabels:
      app: sample-app
  template:
    metadata:
      labels:
        app: sample-app
    spec:
      containers:
      - name: sample-app
        image: nginx:latest
        ports:
        - containerPort: 80
EOF

# Create a kustomization file
cat << EOF > base/kustomization.yaml
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
- deployment.yaml
EOF

# Create environment-specific overlays
for env in dev staging prod; do
  mkdir -p environments/$env
  cat << EOF > environments/$env/kustomization.yaml
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
bases:
- ../../base
namePrefix: $env-
EOF
done

# Commit and push to your Git repository
git add .
git commit -m "Initial infrastructure setup"
# git remote add origin <your-git-repo-url>
# git push -u origin main
```

## Lab Environment Configurations for Different Learning Paths

### Minimal Setup (Resource-Constrained)
- **Local**: Docker Desktop, WSL2 (Windows) or Multipass VM (macOS/Linux)
- **Cloud**: Free tier resources (AWS Free Tier, GCP Free Tier, Azure Free Account)
- **Tools**: Docker, Docker Compose, Terraform, Git
- **Estimated Cost**: $0-10/month

### Standard Setup (Balanced)
- **Local**: Multipass VMs or Vagrant boxes, Minikube
- **Cloud**: Small managed Kubernetes cluster, minimal managed services
- **Tools**: Docker, Kubernetes, Terraform, Ansible, Prometheus, Grafana, CI/CD tools
- **Estimated Cost**: $50-100/month (when active)

### Advanced Setup (Production-Like)
- **Local**: Development tools and clients only
- **Cloud**: Multi-zone Kubernetes cluster, multiple managed services
- **Tools**: Full DevOps toolchain including advanced monitoring, service mesh, security tools
- **Estimated Cost**: $200-300/month (when active)

## Cost Optimization Strategies

1. **Use Cloud Provider Free Tiers**
   - AWS Free Tier: t2.micro instances, limited EBS storage, etc.
   - GCP Free Tier: f1-micro instances, limited storage, etc.
   - Azure Free Account: B1s instances, limited storage, etc.

2. **Implement Automated Shutdown**
   ```bash
   # Create a shutdown script
   cat << 'EOF' > ~/shutdown-lab.sh
   #!/bin/bash
   # Terraform destroy non-persistent resources
   cd ~/terraform/lab
   terraform destroy -auto-approve -target=aws_instance.lab_server
   
   # Or stop EC2 instances
   aws ec2 stop-instances --instance-ids i-1234567890abcdef0
   EOF
   
   chmod +x ~/shutdown-lab.sh
   
   # Create a startup script
   cat << 'EOF' > ~/startup-lab.sh
   #!/bin/bash
   # Terraform apply to recreate resources
   cd ~/terraform/lab
   terraform apply -auto-approve
   
   # Or start EC2 instances
   aws ec2 start-instances --instance-ids i-1234567890abcdef0
   EOF
   
   chmod +x ~/startup-lab.sh
   ```

3. **Use Spot Instances for Non-Critical Workloads**
   ```hcl
   # Terraform configuration for spot instances
   resource "aws_spot_instance_request" "lab_spot" {
     ami                    = "ami-0c55b159cbfafe1f0"
     instance_type          = "t3.medium"
     spot_price             = "0.04"
     wait_for_fulfillment   = true
     key_name               = "your-key-pair"
     subnet_id              = aws_subnet.public_subnet.id
     vpc_security_group_ids = [aws_security_group.lab_sg.id]
     
     tags = {
       Name = "devops-lab-spot"
       Environment = "lab"
     }
   }
   ```

4. **Use Local Resources for Development, Cloud for Testing**
   - Develop and test initially on local resources
   - Move to cloud only for integration testing or performance testing
   - Tear down cloud resources immediately after testing

5. **Leverage Serverless for Appropriate Workloads**
   - Use AWS Lambda, Azure Functions, or Google Cloud Functions for event-driven workloads
   - Implement API Gateway for serverless APIs
   - Use managed databases with serverless tiers

## Best Practices

1. **Infrastructure as Code**: Define all lab resources using IaC tools like Terraform
2. **Version Control**: Store all configurations in Git repositories
3. **Documentation**: Maintain documentation of your lab setup and configurations
4. **Automation**: Automate routine tasks like setup, teardown, and backups
5. **Security**: Implement proper security controls even in lab environments
6. **Resource Tagging**: Tag all cloud resources for tracking and cost allocation
7. **Regular Cleanup**: Periodically review and clean up unused resources
8. **Backup Important Data**: Ensure critical configurations and data are backed up
9. **Isolated Networking**: Use separate VPCs or networks for lab environments
10. **Cost Monitoring**: Set up alerts for unexpected cloud spending

## Common Pitfalls

1. **Leaving Resources Running**: Forgetting to shut down unused cloud resources
2. **Insufficient Security**: Exposing lab environments to the internet without proper controls
3. **Missing Documentation**: Not documenting setup steps, leading to difficulties recreating the environment
4. **Untracked Resources**: Creating resources outside of IaC, making them difficult to manage
5. **Overprovisioning**: Using larger or more powerful resources than necessary
6. **Neglecting Backups**: Losing work due to lack of backups
7. **Configuration Drift**: Manual changes causing differences between IaC and actual state
8. **Ignoring Costs**: Not monitoring cloud spending until receiving a large bill
9. **Complex Initial Setup**: Creating an overly complex environment that's difficult to maintain
10. **Unrealistic Environments**: Lab environments that don't reflect real-world scenarios

## Learning Outcomes

After setting up your lab environment, you should be able to:

1. Create and manage a reproducible DevOps lab environment
2. Use infrastructure as code to define and provision resources
3. Implement cost optimization strategies for cloud resources
4. Configure connectivity between local and cloud environments
5. Install and configure essential DevOps tools
6. Set up a basic GitOps workflow
7. Manage multiple Kubernetes contexts
8. Implement monitoring for your lab environment
9. Automate the creation and teardown of lab resources
10. Document your lab environment for future reference
