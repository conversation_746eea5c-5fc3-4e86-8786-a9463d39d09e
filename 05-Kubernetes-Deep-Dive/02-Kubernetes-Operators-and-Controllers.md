# Kubernetes Operators and Controllers

**Skill Level: Advanced**

## Notes

Kubernetes Operators and Controllers are powerful mechanisms for extending Kubernetes functionality and automating complex application management. While Controllers are a core concept in Kubernetes, Operators build upon this pattern to encode domain-specific knowledge for managing applications.

### Controllers in Kubernetes

Controllers are control loops that watch the state of your cluster, then make or request changes where needed. Each controller tries to move the current cluster state closer to the desired state.

Core Kubernetes controllers include:

1. **Node Controller**: Monitors node health and responds when nodes go down
2. **Replication Controller**: Ensures the correct number of pod replicas are running
3. **Endpoints Controller**: Populates the Endpoints object (joins Services & Pods)
4. **Service Account & Token Controllers**: Create default accounts and API access tokens
5. **Deployment Controller**: Manages the rollout of Deployments
6. **StatefulSet Controller**: Manages StatefulSet resources
7. **Job Controller**: Watches for Job objects and creates Pods to run the specified tasks
8. **CronJob Controller**: Creates Jobs on a time-based schedule

### Kubernetes Operators

Operators extend the controller pattern to manage specific applications and complex stateful workloads. An Operator is a custom controller that uses Custom Resources to manage applications and their components.

Key components of Operators:

1. **Custom Resource Definitions (CRDs)**: Define new resource types in Kubernetes
2. **Custom Controllers**: Watch and manage the custom resources
3. **Domain-Specific Knowledge**: Encode operational knowledge about managing the application

Operators typically handle:

1. **Installation and Updates**: Automated deployment and updates of the application
2. **Backup and Restore**: Managing data backup and recovery
3. **Scaling**: Intelligent scaling based on metrics or schedules
4. **Configuration**: Managing application-specific configuration
5. **Monitoring**: Integrating with monitoring systems
6. **Failure Recovery**: Automated recovery from common failure scenarios

### Operator Framework

The Operator Framework provides tools to build, test, and package Operators:

1. **Operator SDK**: Framework for building Operators
2. **Operator Lifecycle Manager (OLM)**: Helps manage the lifecycle of Operators
3. **Operator Metering**: Usage reporting for Operators
4. **Operator Hub**: Repository of community and commercial Operators

## Practical Example: Building a Custom Operator

### 1. Custom Resource Definition (CRD)

```yaml
# Redis CRD
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: redisclusters.cache.example.com
spec:
  group: cache.example.com
  names:
    kind: RedisCluster
    listKind: RedisClusterList
    plural: redisclusters
    singular: rediscluster
    shortNames:
      - rdcl
  scope: Namespaced
  versions:
    - name: v1
      served: true
      storage: true
      schema:
        openAPIV3Schema:
          type: object
          properties:
            spec:
              type: object
              properties:
                size:
                  type: integer
                  minimum: 1
                  description: The size of the Redis cluster
                version:
                  type: string
                  description: Redis version to use
                persistence:
                  type: boolean
                  description: Enable persistence
                resources:
                  type: object
                  properties:
                    requests:
                      type: object
                      properties:
                        memory:
                          type: string
                        cpu:
                          type: string
                    limits:
                      type: object
                      properties:
                        memory:
                          type: string
                        cpu:
                          type: string
            status:
              type: object
              properties:
                phase:
                  type: string
                  description: Current phase of the cluster
                readyReplicas:
                  type: integer
                  description: Number of ready replicas
                conditions:
                  type: array
                  items:
                    type: object
                    properties:
                      type:
                        type: string
                      status:
                        type: string
                      lastTransitionTime:
                        type: string
                      reason:
                        type: string
                      message:
                        type: string
      subresources:
        status: {}
      additionalPrinterColumns:
        - name: Size
          type: integer
          jsonPath: .spec.size
        - name: Version
          type: string
          jsonPath: .spec.version
        - name: Ready
          type: integer
          jsonPath: .status.readyReplicas
        - name: Phase
          type: string
          jsonPath: .status.phase
        - name: Age
          type: date
          jsonPath: .metadata.creationTimestamp
```

### 2. Custom Resource Instance

```yaml
# Redis Cluster instance
apiVersion: cache.example.com/v1
kind: RedisCluster
metadata:
  name: redis-prod
  namespace: database
spec:
  size: 3
  version: "6.2.6"
  persistence: true
  resources:
    requests:
      memory: "256Mi"
      cpu: "100m"
    limits:
      memory: "512Mi"
      cpu: "200m"
```

### 3. Operator Implementation (Go)

```go
// main.go
package main

import (
	"flag"
	"os"

	"k8s.io/apimachinery/pkg/runtime"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	_ "k8s.io/client-go/plugin/pkg/client/auth/gcp"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/log/zap"

	cachev1 "github.com/example/redis-operator/api/v1"
	"github.com/example/redis-operator/controllers"
)

var (
	scheme   = runtime.NewScheme()
	setupLog = ctrl.Log.WithName("setup")
)

func init() {
	utilruntime.Must(clientgoscheme.AddToScheme(scheme))
	utilruntime.Must(cachev1.AddToScheme(scheme))
}

func main() {
	var metricsAddr string
	var enableLeaderElection bool
	flag.StringVar(&metricsAddr, "metrics-addr", ":8080", "The address the metric endpoint binds to.")
	flag.BoolVar(&enableLeaderElection, "enable-leader-election", false,
		"Enable leader election for controller manager.")
	flag.Parse()

	ctrl.SetLogger(zap.New(zap.UseDevMode(true)))

	mgr, err := ctrl.NewManager(ctrl.GetConfigOrDie(), ctrl.Options{
		Scheme:             scheme,
		MetricsBindAddress: metricsAddr,
		Port:               9443,
		LeaderElection:     enableLeaderElection,
		LeaderElectionID:   "redis-operator-leader-election",
	})
	if err != nil {
		setupLog.Error(err, "unable to start manager")
		os.Exit(1)
	}

	if err = (&controllers.RedisClusterReconciler{
		Client: mgr.GetClient(),
		Log:    ctrl.Log.WithName("controllers").WithName("RedisCluster"),
		Scheme: mgr.GetScheme(),
	}).SetupWithManager(mgr); err != nil {
		setupLog.Error(err, "unable to create controller", "controller", "RedisCluster")
		os.Exit(1)
	}

	setupLog.Info("starting manager")
	if err := mgr.Start(ctrl.SetupSignalHandler()); err != nil {
		setupLog.Error(err, "problem running manager")
		os.Exit(1)
	}
}
```

```go
// controllers/rediscluster_controller.go
package controllers

import (
	"context"
	"fmt"
	"reflect"

	"github.com/go-logr/logr"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"

	cachev1 "github.com/example/redis-operator/api/v1"
)

// RedisClusterReconciler reconciles a RedisCluster object
type RedisClusterReconciler struct {
	client.Client
	Log    logr.Logger
	Scheme *runtime.Scheme
}

// +kubebuilder:rbac:groups=cache.example.com,resources=redisclusters,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=cache.example.com,resources=redisclusters/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=apps,resources=statefulsets,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=core,resources=services,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=core,resources=configmaps,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=core,resources=pods,verbs=get;list;watch

func (r *RedisClusterReconciler) Reconcile(req ctrl.Request) (ctrl.Result, error) {
	ctx := context.Background()
	log := r.Log.WithValues("rediscluster", req.NamespacedName)

	// Fetch the RedisCluster instance
	redisCluster := &cachev1.RedisCluster{}
	err := r.Get(ctx, req.NamespacedName, redisCluster)
	if err != nil {
		if errors.IsNotFound(err) {
			// Request object not found, could have been deleted
			return ctrl.Result{}, nil
		}
		// Error reading the object - requeue the request
		return ctrl.Result{}, err
	}

	// Check if the StatefulSet already exists, if not create a new one
	found := &appsv1.StatefulSet{}
	err = r.Get(ctx, types.NamespacedName{Name: redisCluster.Name, Namespace: redisCluster.Namespace}, found)
	if err != nil && errors.IsNotFound(err) {
		// Define a new StatefulSet
		sts := r.statefulSetForRedis(redisCluster)
		log.Info("Creating a new StatefulSet", "StatefulSet.Namespace", sts.Namespace, "StatefulSet.Name", sts.Name)
		err = r.Create(ctx, sts)
		if err != nil {
			log.Error(err, "Failed to create new StatefulSet", "StatefulSet.Namespace", sts.Namespace, "StatefulSet.Name", sts.Name)
			return ctrl.Result{}, err
		}
		// StatefulSet created successfully - return and requeue
		return ctrl.Result{Requeue: true}, nil
	} else if err != nil {
		log.Error(err, "Failed to get StatefulSet")
		return ctrl.Result{}, err
	}

	// Ensure the StatefulSet size is the same as the spec
	size := redisCluster.Spec.Size
	if *found.Spec.Replicas != size {
		found.Spec.Replicas = &size
		err = r.Update(ctx, found)
		if err != nil {
			log.Error(err, "Failed to update StatefulSet", "StatefulSet.Namespace", found.Namespace, "StatefulSet.Name", found.Name)
			return ctrl.Result{}, err
		}
		// StatefulSet updated - return and requeue
		return ctrl.Result{Requeue: true}, nil
	}

	// Check if the Service already exists, if not create a new one
	service := &corev1.Service{}
	err = r.Get(ctx, types.NamespacedName{Name: redisCluster.Name, Namespace: redisCluster.Namespace}, service)
	if err != nil && errors.IsNotFound(err) {
		// Define a new Service
		svc := r.serviceForRedis(redisCluster)
		log.Info("Creating a new Service", "Service.Namespace", svc.Namespace, "Service.Name", svc.Name)
		err = r.Create(ctx, svc)
		if err != nil {
			log.Error(err, "Failed to create new Service", "Service.Namespace", svc.Namespace, "Service.Name", svc.Name)
			return ctrl.Result{}, err
		}
		// Service created successfully - return and requeue
		return ctrl.Result{Requeue: true}, nil
	} else if err != nil {
		log.Error(err, "Failed to get Service")
		return ctrl.Result{}, err
	}

	// Update the RedisCluster status with the pod names
	// List the pods for this RedisCluster's StatefulSet
	podList := &corev1.PodList{}
	listOpts := []client.ListOption{
		client.InNamespace(redisCluster.Namespace),
		client.MatchingLabels(labelsForRedis(redisCluster.Name)),
	}
	if err = r.List(ctx, podList, listOpts...); err != nil {
		log.Error(err, "Failed to list pods", "RedisCluster.Namespace", redisCluster.Namespace, "RedisCluster.Name", redisCluster.Name)
		return ctrl.Result{}, err
	}

	// Count the pods that are ready
	var readyReplicas int32 = 0
	for _, pod := range podList.Items {
		for _, condition := range pod.Status.Conditions {
			if condition.Type == corev1.PodReady && condition.Status == corev1.ConditionTrue {
				readyReplicas++
				break
			}
		}
	}

	// Update status if needed
	if redisCluster.Status.ReadyReplicas != readyReplicas {
		redisCluster.Status.ReadyReplicas = readyReplicas
		if readyReplicas == redisCluster.Spec.Size {
			redisCluster.Status.Phase = "Ready"
		} else {
			redisCluster.Status.Phase = "Scaling"
		}
		err := r.Status().Update(ctx, redisCluster)
		if err != nil {
			log.Error(err, "Failed to update RedisCluster status")
			return ctrl.Result{}, err
		}
	}

	return ctrl.Result{}, nil
}

// statefulSetForRedis returns a RedisCluster StatefulSet object
func (r *RedisClusterReconciler) statefulSetForRedis(m *cachev1.RedisCluster) *appsv1.StatefulSet {
	ls := labelsForRedis(m.Name)
	replicas := m.Spec.Size

	sts := &appsv1.StatefulSet{
		ObjectMeta: metav1.ObjectMeta{
			Name:      m.Name,
			Namespace: m.Namespace,
		},
		Spec: appsv1.StatefulSetSpec{
			Replicas: &replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: ls,
			},
			ServiceName: m.Name,
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: ls,
				},
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{{
						Image: fmt.Sprintf("redis:%s", m.Spec.Version),
						Name:  "redis",
						Ports: []corev1.ContainerPort{{
							ContainerPort: 6379,
							Name:          "redis",
						}},
						Resources: corev1.ResourceRequirements{
							Requests: corev1.ResourceList{
								corev1.ResourceCPU:    resource.MustParse(m.Spec.Resources.Requests.CPU),
								corev1.ResourceMemory: resource.MustParse(m.Spec.Resources.Requests.Memory),
							},
							Limits: corev1.ResourceList{
								corev1.ResourceCPU:    resource.MustParse(m.Spec.Resources.Limits.CPU),
								corev1.ResourceMemory: resource.MustParse(m.Spec.Resources.Limits.Memory),
							},
						},
					}},
				},
			},
		},
	}
	
	// Set up persistent volume if enabled
	if m.Spec.Persistence {
		sts.Spec.VolumeClaimTemplates = []corev1.PersistentVolumeClaim{
			{
				ObjectMeta: metav1.ObjectMeta{
					Name: "redis-data",
				},
				Spec: corev1.PersistentVolumeClaimSpec{
					AccessModes: []corev1.PersistentVolumeAccessMode{corev1.ReadWriteOnce},
					Resources: corev1.ResourceRequirements{
						Requests: corev1.ResourceList{
							corev1.ResourceStorage: resource.MustParse("1Gi"),
						},
					},
				},
			},
		}
		
		// Add volume mount to container
		sts.Spec.Template.Spec.Containers[0].VolumeMounts = []corev1.VolumeMount{
			{
				Name:      "redis-data",
				MountPath: "/data",
			},
		}
	}
	
	// Set RedisCluster instance as the owner and controller
	controllerutil.SetControllerReference(m, sts, r.Scheme)
	return sts
}

// serviceForRedis returns a RedisCluster Service object
func (r *RedisClusterReconciler) serviceForRedis(m *cachev1.RedisCluster) *corev1.Service {
	ls := labelsForRedis(m.Name)
	
	svc := &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      m.Name,
			Namespace: m.Namespace,
		},
		Spec: corev1.ServiceSpec{
			Selector: ls,
			Ports: []corev1.ServicePort{
				{
					Port:       6379,
					TargetPort: intstr.FromInt(6379),
					Name:       "redis",
				},
			},
		},
	}
	
	// Set RedisCluster instance as the owner and controller
	controllerutil.SetControllerReference(m, svc, r.Scheme)
	return svc
}

// labelsForRedis returns the labels for selecting the resources
func labelsForRedis(name string) map[string]string {
	return map[string]string{"app": "redis", "redis_cr": name}
}

func (r *RedisClusterReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&cachev1.RedisCluster{}).
		Owns(&appsv1.StatefulSet{}).
		Owns(&corev1.Service{}).
		Complete(r)
}
```

### 4. Operator Deployment

```yaml
# Redis Operator Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-operator
  namespace: operators
spec:
  replicas: 1
  selector:
    matchLabels:
      name: redis-operator
  template:
    metadata:
      labels:
        name: redis-operator
    spec:
      serviceAccountName: redis-operator
      containers:
        - name: redis-operator
          image: example.com/redis-operator:v0.1.0
          command:
          - redis-operator
          imagePullPolicy: Always
          env:
            - name: WATCH_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: OPERATOR_NAME
              value: "redis-operator"
          resources:
            limits:
              cpu: 200m
              memory: 256Mi
            requests:
              cpu: 100m
              memory: 128Mi
---
# RBAC Configuration
apiVersion: v1
kind: ServiceAccount
metadata:
  name: redis-operator
  namespace: operators
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: redis-operator
rules:
- apiGroups:
  - ""
  resources:
  - pods
  - services
  - configmaps
  - secrets
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - apps
  resources:
  - statefulsets
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - cache.example.com
  resources:
  - redisclusters
  - redisclusters/status
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: redis-operator
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: redis-operator
subjects:
- kind: ServiceAccount
  name: redis-operator
  namespace: operators
```

### 5. Operator Capability Levels

The Operator Capability Model defines five levels of maturity for Operators:

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           Operator Capability Levels                             │
└─────────────────────────────────────────────────────────────────────────────────┘
                                        │
                                        ▼
┌─────────────────────────────────────────────────────────────────────────────────┐
│ Level 5: Auto Pilot                                                             │
│                                                                                 │
│ - Automatic scaling based on metrics                                            │
│ - Automatic tuning of application parameters                                    │
│ - Predictive analysis for capacity planning                                     │
│ - Automatic detection and remediation of anomalies                              │
└─────────────────────────────────────────────────────────────────────────────────┘
                                        ▲
                                        │
┌─────────────────────────────────────────────────────────────────────────────────┐
│ Level 4: Deep Insights                                                          │
│                                                                                 │
│ - Detailed application metrics, alerts, and logs                                │
│ - Application-specific dashboards                                               │
│ - Capacity planning tools                                                       │
│ - Root cause analysis capabilities                                              │
└─────────────────────────────────────────────────────────────────────────────────┘
                                        ▲
                                        │
┌─────────────────────────────────────────────────────────────────────────────────┐
│ Level 3: Full Lifecycle                                                         │
│                                                                                 │
│ - Automated backup and recovery                                                 │
│ - Failure detection and recovery                                                │
│ - Automated upgrades of the application                                         │
│ - Advanced configuration management                                             │
└─────────────────────────────────────────────────────────────────────────────────┘
                                        ▲
                                        │
┌─────────────────────────────────────────────────────────────────────────────────┐
│ Level 2: Seamless Upgrades                                                      │
│                                                                                 │
│ - Application version upgrades                                                  │
│ - Backup and restore for upgrades                                               │
│ - Minor version upgrades automated                                              │
│ - Configuration changes without restart                                         │
└─────────────────────────────────────────────────────────────────────────────────┘
                                        ▲
                                        │
┌─────────────────────────────────────────────────────────────────────────────────┐
│ Level 1: Basic Install                                                          │
│                                                                                 │
│ - Automated application installation                                            │
│ - Basic configuration options                                                   │
│ - Creation of application resources                                             │
│ - Simple status reporting                                                       │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## Best Practices

1. **Operator Design Principles**
   - Focus on a single application or related set of applications
   - Follow the Kubernetes controller pattern
   - Design for idempotency and eventual consistency
   - Implement proper error handling and recovery
   - Use finalizers for cleanup operations

2. **Custom Resource Design**
   - Define clear, versioned APIs
   - Include validation in CRD schemas
   - Design for backward compatibility
   - Use status subresources for reporting
   - Document fields and their purpose

3. **Controller Implementation**
   - Implement proper reconciliation loops
   - Use owner references for garbage collection
   - Handle edge cases and failure scenarios
   - Implement proper logging and events
   - Use leader election for high availability

4. **Operational Considerations**
   - Implement proper RBAC permissions
   - Use resource requests and limits
   - Implement health checks
   - Provide metrics for monitoring
   - Document operational procedures

5. **Testing and Quality**
   - Write unit tests for controller logic
   - Implement integration tests
   - Test failure scenarios
   - Validate CRD schemas
   - Test upgrades and migrations

6. **Packaging and Distribution**
   - Use OLM for lifecycle management
   - Provide clear documentation
   - Version operators semantically
   - Include examples and tutorials
   - Publish to OperatorHub if appropriate

## Common Pitfalls

1. **Design Issues**
   - Trying to manage too many different resources in one operator
   - Not clearly defining the scope of the operator
   - Overcomplicating the CRD schema
   - Not planning for versioning and upgrades
   - Tight coupling to specific Kubernetes versions

2. **Implementation Problems**
   - Not handling reconciliation errors properly
   - Missing finalizers for cleanup
   - Race conditions in controllers
   - Not using owner references correctly
   - Inefficient watches and event filtering

3. **Operational Challenges**
   - Insufficient logging and monitoring
   - Overly permissive RBAC rules
   - Not handling upgrades of the operator itself
   - Resource leaks during failures
   - Lack of proper documentation

4. **Performance Issues**
   - Inefficient reconciliation loops
   - Too frequent requeuing
   - Excessive API server calls
   - Memory leaks in long-running operators
   - Not implementing proper caching

5. **Security Concerns**
   - Running with excessive privileges
   - Not validating user input
   - Exposing sensitive information in status or logs
   - Not securing communication channels
   - Not implementing proper authentication

6. **Maintenance Challenges**
   - Difficulty upgrading the operator
   - Lack of backward compatibility
   - Poor test coverage
   - Dependency management issues
   - Insufficient documentation for troubleshooting

## Learning Outcomes

After understanding Kubernetes Operators and Controllers, you should be able to:

1. Explain the controller pattern in Kubernetes
2. Understand the role and purpose of Kubernetes Operators
3. Design Custom Resource Definitions for specific applications
4. Implement custom controllers using the Operator pattern
5. Package and deploy Operators using the Operator Lifecycle Manager
6. Evaluate when to use Operators versus other deployment methods
7. Understand the different capability levels of Operators
8. Implement best practices for Operator development
9. Troubleshoot common issues with Operators and Controllers
10. Design Operators that provide advanced application management capabilities
