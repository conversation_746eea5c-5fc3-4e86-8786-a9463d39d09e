# Kubernetes Architecture and Components

**Skill Level: Intermediate**

## Notes

Kubernetes is a powerful container orchestration platform that automates the deployment, scaling, and management of containerized applications. Understanding its architecture and components is essential for DevOps Architects who need to design, implement, and maintain Kubernetes-based systems.

### Kubernetes Architecture Overview

Kubernetes follows a master-worker architecture (also known as control plane and data plane):

1. **Control Plane (Master)**: Manages the cluster and maintains the desired state
2. **Data Plane (Worker Nodes)**: Runs the actual workloads (containers)

### Control Plane Components

1. **API Server**: The front-end of the Kubernetes control plane that exposes the Kubernetes API. All communications with the cluster go through the API server.

2. **etcd**: A distributed key-value store that stores all cluster data. It's the single source of truth for the cluster state.

3. **Scheduler**: Watches for newly created Pods with no assigned node and selects a node for them to run on based on resource requirements, constraints, and other factors.

4. **Controller Manager**: Runs controller processes that regulate the state of the cluster. Examples include:
   - Node Controller: Monitors node health
   - Replication Controller: Ensures the desired number of pod replicas are running
   - Endpoints Controller: Populates the Endpoints object (joins Services & Pods)
   - Service Account & Token Controllers: Create default accounts and API access tokens

5. **Cloud Controller Manager**: Interfaces with the underlying cloud provider's API to manage resources like load balancers and storage.

### Worker Node Components

1. **Kubelet**: An agent that runs on each node and ensures containers are running in a Pod as expected. It communicates with the API server and manages containers according to PodSpecs.

2. **Container Runtime**: The software responsible for running containers (e.g., containerd, CRI-O, Docker).

3. **Kube-proxy**: Maintains network rules on nodes to allow network communication to Pods from inside or outside the cluster.

### Kubernetes Objects and Resources

1. **Pods**: The smallest deployable units in Kubernetes that can be created and managed. A Pod contains one or more containers that share storage, network, and a specification for how to run.

2. **ReplicaSets**: Ensures that a specified number of Pod replicas are running at any given time.

3. **Deployments**: Provides declarative updates for Pods and ReplicaSets, allowing for controlled rollouts and rollbacks.

4. **StatefulSets**: Manages the deployment and scaling of a set of Pods with unique, persistent identities and stable hostnames.

5. **DaemonSets**: Ensures that all (or some) nodes run a copy of a Pod, typically used for cluster-wide services like monitoring or logging.

6. **Jobs and CronJobs**: Run tasks that execute once or periodically according to a schedule.

7. **Services**: An abstraction that defines a logical set of Pods and a policy to access them.
   - ClusterIP: Exposes the service on an internal IP
   - NodePort: Exposes the service on each node's IP at a static port
   - LoadBalancer: Exposes the service externally using a cloud provider's load balancer
   - ExternalName: Maps the service to a DNS name

8. **Ingress**: Manages external access to services, typically HTTP, providing load balancing, SSL termination, and name-based virtual hosting.

9. **ConfigMaps and Secrets**: Store configuration data and sensitive information, respectively, that can be consumed by Pods.

10. **Volumes**: Provide persistent storage for Pods, allowing data to survive container restarts.

11. **Namespaces**: Virtual clusters within a physical cluster, providing a way to divide cluster resources.

12. **Custom Resources**: Extensions of the Kubernetes API that allow for custom objects to be stored in the Kubernetes cluster.

## Practical Example: Kubernetes Cluster Architecture

```yaml
# Example of a production-grade Kubernetes cluster architecture

# Control Plane Configuration
apiVersion: kubeadm.k8s.io/v1beta3
kind: ClusterConfiguration
metadata:
  name: production-cluster
kubernetesVersion: v1.24.0
apiServer:
  extraArgs:
    authorization-mode: "Node,RBAC"
    enable-admission-plugins: "NodeRestriction,PodSecurityPolicy"
    audit-log-path: "/var/log/kubernetes/audit.log"
    audit-log-maxage: "30"
    audit-log-maxbackup: "10"
    audit-log-maxsize: "100"
  extraVolumes:
  - name: "audit-logs"
    hostPath: "/var/log/kubernetes"
    mountPath: "/var/log/kubernetes"
    readOnly: false
controllerManager:
  extraArgs:
    node-monitor-period: "5s"
    node-monitor-grace-period: "40s"
    pod-eviction-timeout: "5m0s"
    cluster-signing-duration: "87600h0m0s" # 10 years
scheduler:
  extraArgs:
    address: "0.0.0.0"
etcd:
  local:
    dataDir: "/var/lib/etcd"
    extraArgs:
      auto-compaction-retention: "8"
      quota-backend-bytes: "8589934592" # 8GB
networking:
  podSubnet: "**********/16"
  serviceSubnet: "*********/12"
  dnsDomain: "cluster.local"
---
# Worker Node Configuration
apiVersion: kubelet.config.k8s.io/v1beta1
kind: KubeletConfiguration
authentication:
  anonymous:
    enabled: false
  webhook:
    enabled: true
  x509:
    clientCAFile: "/etc/kubernetes/pki/ca.crt"
authorization:
  mode: Webhook
clusterDomain: "cluster.local"
clusterDNS:
- "**********"
runtimeRequestTimeout: "15m"
tlsCertFile: "/etc/kubernetes/pki/kubelet.crt"
tlsPrivateKeyFile: "/etc/kubernetes/pki/kubelet.key"
resolvConf: "/etc/resolv.conf"
rotateCertificates: true
serverTLSBootstrap: true
---
# Example Pod
apiVersion: v1
kind: Pod
metadata:
  name: nginx
  namespace: default
  labels:
    app: nginx
spec:
  containers:
  - name: nginx
    image: nginx:1.21
    ports:
    - containerPort: 80
    resources:
      requests:
        memory: "64Mi"
        cpu: "250m"
      limits:
        memory: "128Mi"
        cpu: "500m"
    volumeMounts:
    - name: nginx-config
      mountPath: /etc/nginx/conf.d
  volumes:
  - name: nginx-config
    configMap:
      name: nginx-config
---
# Example Service
apiVersion: v1
kind: Service
metadata:
  name: nginx-service
spec:
  selector:
    app: nginx
  ports:
  - port: 80
    targetPort: 80
  type: ClusterIP
---
# Example Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-deployment
  labels:
    app: nginx
spec:
  replicas: 3
  selector:
    matchLabels:
      app: nginx
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx
        image: nginx:1.21
        ports:
        - containerPort: 80
        resources:
          requests:
            memory: "64Mi"
            cpu: "250m"
          limits:
            memory: "128Mi"
            cpu: "500m"
```

### Kubernetes Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           Kubernetes Cluster                                     │
└─────────────────────────────────────────────────────────────────────────────────┘
                                        │
                ┌─────────────────────────────────────────────┐
                │                                             │
                ▼                                             ▼
┌─────────────────────────────────┐             ┌─────────────────────────────────┐
│        Control Plane            │             │          Worker Nodes           │
│                                 │             │                                 │
│  ┌─────────────┐               │             │  ┌─────────────┐  ┌───────────┐ │
│  │             │               │             │  │             │  │           │ │
│  │ API Server  │◄──────────────┼─────────────┼─►│  Kubelet    │  │  Pod 1    │ │
│  │             │               │             │  │             │  │           │ │
│  └─────────────┘               │             │  └─────────────┘  └───────────┘ │
│        │  ▲                    │             │        │                        │
│        │  │                    │             │        │           ┌───────────┐ │
│        │  │                    │             │        │           │           │ │
│        │  │                    │             │        └──────────►│  Pod 2    │ │
│        │  │                    │             │                    │           │ │
│        │  │                    │             │                    └───────────┘ │
│        │  │                    │             │                                 │
│        │  │                    │             │  ┌─────────────┐                │
│        │  │                    │             │  │             │                │
│        │  │                    │             │  │ Container   │                │
│        ▼  │                    │             │  │ Runtime     │                │
│  ┌─────────────┐               │             │  │             │                │
│  │             │               │             │  └─────────────┘                │
│  │  Scheduler  │               │             │                                 │
│  │             │               │             │  ┌─────────────┐                │
│  └─────────────┘               │             │  │             │                │
│                                │             │  │ Kube-proxy  │                │
│  ┌─────────────┐               │             │  │             │                │
│  │ Controller  │               │             │  └─────────────┘                │
│  │ Manager     │               │             │                                 │
│  │             │               │             └─────────────────────────────────┘
│  └─────────────┘               │                             ▲
│                                │                             │
│  ┌─────────────┐               │                             │
│  │             │               │                             │
│  │    etcd     │               │                             │
│  │             │               │                             │
│  └─────────────┘               │                             │
│                                │                             │
└─────────────────────────────────┘                             │
                │                                              │
                └──────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────────┐
│                           Kubernetes Objects                                     │
└─────────────────────────────────────────────────────────────────────────────────┘
                                        │
                                        ▼
┌─────────────────────────────────────────────────────────────────────────────────┐
│                                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │             │    │             │    │             │    │             │      │
│  │   Pods      │    │ ReplicaSets │    │ Deployments │    │StatefulSets │      │
│  │             │    │             │    │             │    │             │      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│                                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │             │    │             │    │             │    │             │      │
│  │ DaemonSets  │    │  Services   │    │   Ingress   │    │ ConfigMaps  │      │
│  │             │    │             │    │             │    │ & Secrets   │      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## Best Practices

1. **Control Plane High Availability**
   - Deploy multiple control plane nodes (at least 3 for quorum)
   - Distribute control plane nodes across availability zones
   - Use load balancers for API server access
   - Implement etcd clustering with proper backup strategies

2. **Node Management**
   - Use node labels and taints for workload placement
   - Implement proper resource requests and limits
   - Configure node auto-scaling based on workload demands
   - Regularly update and patch nodes

3. **Workload Design**
   - Use Deployments for stateless applications
   - Use StatefulSets for stateful applications
   - Implement proper health checks (liveness and readiness probes)
   - Design for horizontal scaling rather than vertical scaling

4. **Networking**
   - Choose appropriate network plugins based on requirements
   - Implement network policies for segmentation
   - Use services and ingress controllers appropriately
   - Consider service mesh for complex microservice architectures

5. **Security**
   - Implement RBAC for access control
   - Use Pod Security Policies or Pod Security Standards
   - Regularly scan container images for vulnerabilities
   - Encrypt sensitive data using Secrets and etcd encryption

6. **Monitoring and Logging**
   - Implement comprehensive monitoring for cluster components
   - Collect and centralize logs from all components
   - Set up alerts for critical issues
   - Monitor both cluster health and application performance

7. **Backup and Disaster Recovery**
   - Regularly backup etcd data
   - Implement disaster recovery procedures
   - Test recovery processes periodically
   - Consider multi-cluster strategies for critical workloads

## Common Pitfalls

1. **Resource Management Issues**
   - Not setting resource requests and limits
   - Over-provisioning or under-provisioning resources
   - Ignoring quality of service (QoS) classes
   - Not monitoring resource usage trends

2. **Networking Challenges**
   - Choosing inappropriate network plugins
   - Overlooking network policies for security
   - DNS configuration issues
   - Service discovery problems

3. **Security Vulnerabilities**
   - Running containers as root
   - Not implementing RBAC properly
   - Using outdated container images with known vulnerabilities
   - Exposing the Kubernetes API server without proper authentication

4. **Operational Complexity**
   - Managing too many clusters
   - Lack of standardization across environments
   - Manual intervention in cluster operations
   - Insufficient documentation and runbooks

5. **Upgrade Challenges**
   - Not testing upgrades in non-production environments
   - Skipping multiple versions in a single upgrade
   - Not having rollback plans
   - Ignoring component version compatibility

6. **Monitoring Gaps**
   - Insufficient monitoring of control plane components
   - Not monitoring etcd performance
   - Missing alerts for critical conditions
   - Lack of visibility into application performance

7. **Scalability Issues**
   - Hitting etcd performance limits
   - API server bottlenecks
   - Scheduler inefficiencies with large clusters
   - Network plugin limitations at scale

## Learning Outcomes

After understanding Kubernetes architecture and components, you should be able to:

1. Describe the key components of the Kubernetes control plane and worker nodes
2. Explain the responsibilities of each Kubernetes component
3. Understand the relationship between different Kubernetes objects
4. Design Kubernetes deployments that follow best practices
5. Troubleshoot common issues in Kubernetes clusters
6. Make informed decisions about cluster architecture and configuration
7. Implement high availability for Kubernetes components
8. Understand the security implications of different Kubernetes configurations
9. Design effective networking strategies for Kubernetes workloads
10. Plan for scalability and performance in Kubernetes environments
