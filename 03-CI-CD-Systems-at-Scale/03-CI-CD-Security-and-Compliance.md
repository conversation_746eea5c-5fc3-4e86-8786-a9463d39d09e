# CI/CD Security and Compliance

**Skill Level: Advanced**

## Notes

Securing CI/CD pipelines is critical as they represent a high-value target for attackers. A compromised pipeline can lead to malicious code being deployed to production environments. Additionally, organizations must ensure their CI/CD processes comply with relevant regulations and standards. This topic explores strategies for securing CI/CD pipelines and ensuring compliance at scale.

### CI/CD Security Concerns

1. **Supply Chain Attacks**: Compromised dependencies or base images
2. **Pipeline Infiltration**: Unauthorized access to CI/CD systems
3. **Credential Theft**: Exposure of sensitive credentials and secrets
4. **Malicious Code Injection**: Insertion of malicious code during the build process
5. **Infrastructure Tampering**: Unauthorized changes to deployment infrastructure
6. **Artifact Tampering**: Modification of build artifacts before deployment
7. **Insecure Configurations**: Misconfigured CI/CD tools exposing vulnerabilities

### Compliance Requirements

1. **Audit Trails**: Comprehensive logging of all pipeline activities
2. **Separation of Duties**: Ensuring no single person can compromise the entire pipeline
3. **Approval Workflows**: Requiring approvals for sensitive deployments
4. **Policy Enforcement**: Ensuring all deployments meet organizational policies
5. **Evidence Collection**: Gathering evidence of security and compliance checks
6. **Vulnerability Management**: Tracking and addressing vulnerabilities
7. **Access Control**: Restricting access to CI/CD systems and artifacts

## Practical Example: Secure CI/CD Pipeline Implementation

```yaml
# Example of a secure CI/CD pipeline with GitHub Actions

name: Secure CI/CD Pipeline

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  security-checks:
    name: Security Checks
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          # Fetch full history for integrity checks
          fetch-depth: 0
      
      - name: Verify Git commit signatures
        run: |
          git verify-commit HEAD
      
      - name: Detect secrets in code
        uses: gitleaks/gitleaks-action@v2
      
      - name: Run SAST scan
        uses: github/codeql-action/analyze@v2
        with:
          languages: javascript, python

  build-and-test:
    name: Build and Test
    needs: security-checks
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '16'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests with coverage
        run: npm test -- --coverage
      
      - name: Upload test results
        uses: actions/upload-artifact@v3
        with:
          name: test-results
          path: coverage/
      
      - name: Build application
        run: npm run build
      
      - name: Upload build artifact
        uses: actions/upload-artifact@v3
        with:
          name: build-artifact
          path: build/

  container-build:
    name: Build and Scan Container
    needs: build-and-test
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
      id-token: write # Needed for keyless signing
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Download build artifact
        uses: actions/download-artifact@v3
        with:
          name: build-artifact
          path: build/
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: ghcr.io/${{ github.repository }}:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
      
      - name: Install Cosign
        uses: sigstore/cosign-installer@v3
      
      - name: Sign the container image
        run: |
          cosign sign --key env://COSIGN_PRIVATE_KEY ghcr.io/${{ github.repository }}:${{ github.sha }}
        env:
          COSIGN_PRIVATE_KEY: ${{ secrets.COSIGN_PRIVATE_KEY }}
          COSIGN_PASSWORD: ${{ secrets.COSIGN_PASSWORD }}
      
      - name: Scan image for vulnerabilities
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ghcr.io/${{ github.repository }}:${{ github.sha }}
          format: 'sarif'
          output: 'trivy-results.sarif'
          severity: 'CRITICAL,HIGH'
          exit-code: '1'
          ignore-unfixed: true

  compliance-checks:
    name: Compliance Checks
    needs: container-build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Run policy checks
        uses: open-policy-agent/conftest-action@v2
        with:
          files: k8s/*.yaml
          policy: policy/
      
      - name: Generate SBOM
        uses: anchore/sbom-action@v0
        with:
          image: ghcr.io/${{ github.repository }}:${{ github.sha }}
          artifact-name: sbom.spdx.json
          upload-artifact: true
      
      - name: Compliance report
        run: |
          echo "Generating compliance report"
          # Script to generate compliance report based on all checks
          # This would typically collect evidence from all previous steps
          echo "Compliance report generated" > compliance-report.txt
      
      - name: Upload compliance report
        uses: actions/upload-artifact@v3
        with:
          name: compliance-report
          path: compliance-report.txt

  deploy-approval:
    name: Deployment Approval
    needs: compliance-checks
    runs-on: ubuntu-latest
    environment: production # This creates a manual approval gate
    steps:
      - name: Approval notification
        run: echo "Deployment has been approved"

  deploy:
    name: Deploy to Production
    needs: deploy-approval
    runs-on: ubuntu-latest
    steps:
      - name: Checkout infrastructure code
        uses: actions/checkout@v3
        with:
          repository: ${{ github.repository_owner }}/infrastructure
          token: ${{ secrets.DEPLOY_PAT }}
      
      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
      
      - name: Update deployment configuration
        run: |
          # Update the image tag in the deployment configuration
          sed -i 's|image: ghcr.io/${{ github.repository }}:.*|image: ghcr.io/${{ github.repository }}:${{ github.sha }}|' environments/production/deployment.yaml
      
      - name: Verify image signature
        run: |
          cosign verify --key cosign.pub ghcr.io/${{ github.repository }}:${{ github.sha }}
      
      - name: Deploy to production
        run: |
          # Apply the updated configuration
          terraform init
          terraform apply -auto-approve -var="image_tag=${{ github.sha }}"
      
      - name: Record deployment
        run: |
          # Record deployment details for audit purposes
          echo "Deployed version ${{ github.sha }} to production at $(date)" >> deployment-log.txt
      
      - name: Upload deployment log
        uses: actions/upload-artifact@v3
        with:
          name: deployment-log
          path: deployment-log.txt
```

### Security Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           Secure CI/CD Architecture                              │
└─────────────────────────────────────────────────────────────────────────────────┘
                                        │
                                        ▼
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              Source Code Protection                              │
│                                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │             │    │             │    │             │    │             │      │
│  │ Signed      │    │ Branch      │    │ Code        │    │ Secret      │      │
│  │ Commits     │    │ Protection  │    │ Reviews     │    │ Scanning    │      │
│  │             │    │             │    │             │    │             │      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
                                        │
                                        ▼
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              Build Security                                      │
│                                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │             │    │             │    │             │    │             │      │
│  │ Isolated    │    │ Dependency  │    │ SAST        │    │ Secure      │      │
│  │ Build Env   │    │ Scanning    │    │ Analysis    │    │ Credentials │      │
│  │             │    │             │    │             │    │             │      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
                                        │
                                        ▼
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              Artifact Security                                   │
│                                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │             │    │             │    │             │    │             │      │
│  │ Signed      │    │ Immutable   │    │ Vulnerability│   │ SBOM        │      │
│  │ Artifacts   │    │ Artifacts   │    │ Scanning    │    │ Generation  │      │
│  │             │    │             │    │             │    │             │      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
                                        │
                                        ▼
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              Deployment Security                                 │
│                                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │             │    │             │    │             │    │             │      │
│  │ Signature   │    │ Policy      │    │ Approval    │    │ Deployment  │      │
│  │ Verification│    │ Enforcement │    │ Gates       │    │ Auditing    │      │
│  │             │    │             │    │             │    │             │      │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘      │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## Best Practices

1. **Least Privilege Access**: Grant minimal permissions to CI/CD systems and processes
2. **Secure Credential Management**: Use a secure vault for storing and rotating credentials
3. **Immutable Artifacts**: Create immutable, versioned artifacts that cannot be modified after creation
4. **Artifact Signing**: Sign build artifacts to verify their authenticity and integrity
5. **Supply Chain Verification**: Verify the integrity of dependencies and base images
6. **Infrastructure as Code Security**: Scan IaC templates for security issues
7. **Ephemeral Build Environments**: Use fresh, ephemeral environments for each build
8. **Comprehensive Logging**: Maintain detailed audit logs of all pipeline activities
9. **Separation of Environments**: Isolate development, testing, and production environments
10. **Multi-factor Authentication**: Require MFA for accessing CI/CD systems
11. **Automated Compliance Checks**: Automate policy enforcement and compliance verification
12. **Regular Security Testing**: Conduct regular penetration testing of CI/CD systems

## Common Pitfalls

1. **Hardcoded Secrets**: Embedding credentials directly in pipeline configurations or code
2. **Excessive Permissions**: Granting overly broad permissions to CI/CD systems
3. **Inadequate Isolation**: Insufficient isolation between build environments
4. **Bypassing Security Checks**: Allowing manual overrides of security controls
5. **Outdated Dependencies**: Using outdated tools or dependencies with known vulnerabilities
6. **Insufficient Logging**: Not capturing enough information for effective auditing
7. **Lack of Verification**: Not verifying the integrity of artifacts before deployment
8. **Insecure Communication**: Using unencrypted channels for transferring artifacts
9. **Manual Processes**: Relying on manual processes for security-critical tasks
10. **Neglecting Developer Experience**: Making security so cumbersome that developers seek workarounds
11. **Inconsistent Enforcement**: Applying security controls inconsistently across projects
12. **Focusing Only on Code**: Neglecting infrastructure and configuration security

## Learning Outcomes

After understanding CI/CD security and compliance, you should be able to:

1. Identify and mitigate security risks in CI/CD pipelines
2. Implement secure credential management for CI/CD systems
3. Design pipelines that enforce security and compliance requirements
4. Create audit trails that satisfy regulatory requirements
5. Implement artifact signing and verification
6. Balance security requirements with developer productivity
7. Automate security and compliance checks in CI/CD pipelines
8. Develop incident response plans for CI/CD security breaches
9. Evaluate and select appropriate tools for securing CI/CD pipelines
10. Create a comprehensive strategy for CI/CD security and compliance
