# Microservices with GitOps on AWS EKS Case Study

**Skill Level: Advanced**

## Notes

This case study examines the journey of TechNova, a rapidly growing SaaS company, as they migrated from a monolithic application to a microservices architecture deployed on AWS EKS using GitOps principles. The case study covers the entire process from initial planning through implementation and ongoing operations, highlighting key architectural decisions, challenges encountered, and lessons learned.

## Company Background

**TechNova** is a B2B SaaS company providing an enterprise resource planning (ERP) solution for mid-sized manufacturing companies. Founded in 2015, the company experienced rapid growth, reaching over 200 customers and 150 employees by 2021.

### Initial State

- **Architecture**: Monolithic Java application with a PostgreSQL database
- **Deployment Process**: Manual deployments every 2-3 weeks
- **Infrastructure**: Traditional EC2 instances with auto-scaling groups
- **Team Structure**: Siloed development and operations teams
- **Challenges**:
  - Slow release cycles (2-3 weeks)
  - Deployment failures (~20% of deployments)
  - Scaling issues during peak usage
  - Difficulty onboarding new developers
  - Limited ability to adopt new technologies
  - Growing technical debt

### Business Drivers for Change

- Need to accelerate feature delivery
- Improve system reliability and scalability
- Enable teams to work independently
- Support rapid company growth
- Modernize technology stack
- Improve developer experience

## Architecture and Design

### Microservices Architecture

The team decided to adopt a domain-driven design approach to decompose the monolith into microservices. They identified the following core domains:

```mermaid
graph TD
    subgraph "Customer-Facing Services"
        A[User Management]
        B[Authentication]
        C[Tenant Management]
    end

    subgraph "Core Business Services"
        D[Inventory Management]
        E[Order Processing]
        F[Manufacturing Planning]
        G[Supply Chain]
        H[Reporting]
    end

    subgraph "Integration Services"
        I[API Gateway]
        J[Event Bus]
        K[External Integrations]
    end

    subgraph "Data Services"
        L[Data Lake]
        M[Analytics]
        N[Search]
    end

    I --> A
    I --> B
    I --> C
    I --> D
    I --> E
    I --> F
    I --> G
    I --> H

    A --> J
    B --> J
    C --> J
    D --> J
    E --> J
    F --> J
    G --> J
    H --> J

    J --> K
    J --> L
    L --> M
    M --> N
```

### Technology Stack Selection

After evaluating various options, the team selected the following technology stack:

| Component | Technology | Rationale |
|-----------|------------|-----------|
| Container Orchestration | AWS EKS | Managed Kubernetes with AWS integration |
| Service Mesh | AWS App Mesh | Native AWS integration, traffic management |
| CI/CD | GitHub Actions + Flux | GitHub for CI, Flux for GitOps CD |
| Containerization | Docker | Industry standard, developer familiarity |
| API Gateway | Amazon API Gateway | Managed service, easy integration with AWS services |
| Messaging | Amazon SNS/SQS | Managed, scalable messaging services |
| Databases | Amazon RDS, DynamoDB | Mix of relational and NoSQL for different use cases |
| Monitoring | Prometheus, Grafana | Industry standard, comprehensive monitoring |
| Logging | Fluent Bit, Elasticsearch | Efficient log collection and analysis |
| Secret Management | AWS Secrets Manager | Native AWS integration |
| Infrastructure as Code | Terraform | Multi-cloud support, strong ecosystem |

### Architecture Decision Records (ADRs)

The team documented their key technology decisions using Architecture Decision Records (ADRs) to provide context and rationale for future team members. Here are the ADRs for the major components:

#### ADR-001: Container Orchestration Platform

**Status**: Accepted

**Context**:
TechNova needs a container orchestration platform to manage the deployment, scaling, and operations of application containers across clusters of hosts. The platform should be reliable, scalable, and integrate well with AWS services.

**Decision**:
We will use Amazon Elastic Kubernetes Service (EKS) as our container orchestration platform.

**Rationale**:
- Managed Kubernetes service reduces operational overhead
- Native integration with AWS services (IAM, VPC, ELB, etc.)
- Supports both EC2 and Fargate launch types for flexibility
- Kubernetes is the industry standard with broad community support
- Automatic control plane management and upgrades
- Multi-AZ support for high availability
- Supports our GitOps workflow requirements

**Alternatives Considered**:
1. **Amazon ECS**:
   - Simpler than Kubernetes but less flexible
   - Tighter AWS integration but more AWS-specific
   - Less community support and ecosystem

2. **Self-managed Kubernetes on EC2**:
   - More control but significantly higher operational burden
   - Requires expertise to manage the control plane
   - No SLA for the Kubernetes control plane

3. **Google Kubernetes Engine (GKE)**:
   - More mature Kubernetes offering
   - Would require multi-cloud strategy
   - Less integration with our existing AWS services

**Consequences**:
- Team will need to develop Kubernetes expertise
- Will need to implement proper security controls for Kubernetes
- May face complexity in initial setup and configuration
- Will benefit from the extensive Kubernetes ecosystem
- Can leverage existing AWS security and networking controls

#### ADR-002: GitOps Deployment Tool

**Status**: Accepted

**Context**:
We need a GitOps tool that can automatically synchronize our Kubernetes clusters with Git repositories containing our desired application state. This tool should support our multi-environment deployment strategy and provide observability into the synchronization process.

**Decision**:
We will use Flux v2 as our GitOps deployment tool.

**Rationale**:
- Purpose-built for GitOps with Kubernetes
- Supports multi-tenancy for our different teams and services
- Provides Helm, Kustomize, and plain YAML support
- Offers notification capabilities for deployment events
- Supports progressive delivery through Flagger integration
- CNCF incubating project with strong community support
- Declarative and Kubernetes-native API

**Alternatives Considered**:
1. **Argo CD**:
   - Similar capabilities to Flux
   - More mature UI but less native GitOps focus
   - Would require additional components for notifications and progressive delivery

2. **Jenkins X**:
   - More comprehensive CI/CD platform
   - Higher complexity and resource requirements
   - Less focused on the pure GitOps model we want to implement

3. **AWS CodePipeline with CodeBuild**:
   - Native AWS integration
   - Push-based rather than pull-based deployment model
   - Less alignment with GitOps principles

**Consequences**:
- Will need to implement proper RBAC for Flux controllers
- Need to establish Git repository structure that works well with Flux
- Will gain automated synchronization between Git and clusters
- Can implement progressive delivery patterns
- Will need to integrate with our existing CI processes

#### ADR-003: Service Mesh Technology

**Status**: Accepted

**Context**:
As we move to a microservices architecture, we need a service mesh to handle service-to-service communication, implement traffic control, and provide observability into our service interactions.

**Decision**:
We will use AWS App Mesh as our service mesh technology.

**Rationale**:
- Native integration with AWS services (EKS, CloudWatch, X-Ray)
- Supports both Kubernetes and non-Kubernetes workloads
- Implementation of Envoy proxy (industry standard)
- Simplified management through AWS Console and APIs
- Cost-effective with no additional control plane charges
- Supports canary deployments and traffic splitting
- Integrates with our existing AWS security controls

**Alternatives Considered**:
1. **Istio**:
   - More feature-rich but significantly more complex
   - Steeper learning curve and higher resource requirements
   - Larger community and ecosystem
   - Less native integration with AWS services

2. **Linkerd**:
   - Lightweight and easier to operate than Istio
   - Less feature-rich than App Mesh for our specific needs
   - Less integration with AWS services

3. **No Service Mesh**:
   - Simpler initial implementation
   - Would require building custom solutions for traffic management
   - Would lack comprehensive observability between services

**Consequences**:
- Will need to deploy the App Mesh controller in our EKS clusters
- Services will require sidecar proxies
- Will gain advanced traffic management capabilities
- Can implement more sophisticated deployment strategies
- Will have better observability into service-to-service communication
- May face some complexity in initial configuration

#### ADR-004: Continuous Integration Platform

**Status**: Accepted

**Context**:
We need a CI platform that integrates well with our GitHub repositories, supports containerized builds, and can be easily integrated into our GitOps workflow.

**Decision**:
We will use GitHub Actions as our continuous integration platform.

**Rationale**:
- Tight integration with our GitHub repositories
- No additional infrastructure to manage
- Supports matrix builds for testing across multiple configurations
- Native container support
- Extensive marketplace of pre-built actions
- Easy secrets management
- Flexible workflow configuration as code

**Alternatives Considered**:
1. **Jenkins**:
   - More customizable but requires dedicated infrastructure
   - Steeper learning curve and higher maintenance
   - Rich plugin ecosystem but less standardized

2. **AWS CodeBuild**:
   - Native AWS integration
   - Pay-per-minute pricing model
   - Less integrated with our GitHub workflow

3. **CircleCI**:
   - Mature cloud-based CI platform
   - Additional cost and integration point
   - Less native integration with GitHub

**Consequences**:
- Will need to design workflows that fit GitHub Actions' execution model
- May hit limitations with very complex build requirements
- Will benefit from simplified management and tight GitHub integration
- Will need to implement proper secrets management
- Can leverage existing GitHub RBAC for access control

#### ADR-005: Infrastructure as Code Tool

**Status**: Accepted

**Context**:
We need an Infrastructure as Code (IaC) tool to manage our AWS infrastructure in a consistent, version-controlled manner that supports our multi-environment strategy.

**Decision**:
We will use Terraform for infrastructure provisioning and management.

**Rationale**:
- Declarative approach to infrastructure definition
- Strong provider ecosystem, especially for AWS
- State management for tracking infrastructure changes
- Plan/apply workflow for reviewing changes before implementation
- Modules support for reusable infrastructure components
- Multi-cloud support for potential future expansion
- Large community and extensive documentation

**Alternatives Considered**:
1. **AWS CloudFormation**:
   - Native AWS integration
   - Less flexible for multi-cloud scenarios
   - JSON/YAML syntax less expressive than HCL

2. **Pulumi**:
   - Programmatic approach using familiar languages
   - Less mature ecosystem and community
   - Higher learning curve for infrastructure team

3. **Kubernetes-native tools (e.g., Crossplane)**:
   - Kubernetes-centric approach
   - Less mature for managing non-Kubernetes AWS resources
   - Would require additional expertise

**Consequences**:
- Will need to implement proper Terraform state management
- Will need to establish module structure and standards
- Will gain consistent, repeatable infrastructure deployments
- Can implement infrastructure testing and validation
- Will need to integrate with our GitOps workflow
- Will need to manage Terraform execution permissions carefully

#### ADR-006: Observability Stack

**Status**: Accepted

**Context**:
With a distributed microservices architecture, we need comprehensive observability to understand system behavior, troubleshoot issues, and ensure performance and reliability.

**Decision**:
We will implement a observability stack consisting of Prometheus, Grafana, Fluent Bit, and Elasticsearch.

**Rationale**:
- Prometheus provides robust metrics collection with a pull-based model
- Grafana offers flexible visualization and dashboarding
- Fluent Bit provides efficient log collection with low resource usage
- Elasticsearch enables powerful log search and analysis
- All components are open-source with strong community support
- Stack can be deployed within our Kubernetes clusters
- Components integrate well with our AWS infrastructure

**Alternatives Considered**:
1. **AWS-native observability (CloudWatch, X-Ray)**:
   - Tighter AWS integration
   - Higher cost at our scale
   - Less flexibility and control

2. **Datadog**:
   - Comprehensive SaaS observability platform
   - Simplified management but higher cost
   - Less control over data retention and storage

3. **Elastic Cloud on Kubernetes (ECK)**:
   - More integrated Elastic Stack management
   - Higher resource requirements
   - More complex to operate

**Consequences**:
- Will need to deploy and manage observability components in our clusters
- Will need to establish standards for metrics and logging
- Will gain comprehensive visibility into our distributed system
- Can implement advanced alerting and dashboarding
- Will need to manage storage and retention for metrics and logs
- Will need to train team on effective use of observability tools

### AWS EKS Architecture

The team designed a multi-environment EKS architecture:

```mermaid
graph TD
    subgraph "AWS Cloud"
        subgraph "Shared Services"
            A[AWS ECR]
            B[AWS Route 53]
            C[AWS WAF]
            D[AWS CloudTrail]
        end

        subgraph "Development Environment"
            E[Dev VPC]
            F[Dev EKS Cluster]
            G[Dev Databases]
        end

        subgraph "Staging Environment"
            H[Staging VPC]
            I[Staging EKS Cluster]
            J[Staging Databases]
        end

        subgraph "Production Environment"
            K[Prod VPC]
            L[Prod EKS Cluster]
            M[Prod Databases]

            subgraph "Prod EKS Cluster Details"
                N[Control Plane]
                O[Worker Node Group 1]
                P[Worker Node Group 2]
                Q[Worker Node Group 3]

                R[CoreDNS]
                S[AWS Load Balancer Controller]
                T[Cluster Autoscaler]
                U[External DNS]
                V[Flux GitOps Operator]
                W[Prometheus/Grafana]
                X[AWS App Mesh Controller]
            end
        end
    end

    A --> F
    A --> I
    A --> L

    B --> C
    C --> E
    C --> H
    C --> K

    N --> O
    N --> P
    N --> Q

    V --> O
    V --> P
    V --> Q
```

## GitOps Implementation

### GitOps Principles Adopted

1. **Declarative Infrastructure**: All infrastructure and application configurations defined as code
2. **Version Controlled**: All configurations stored in Git repositories
3. **Automated Synchronization**: Automatic deployment of changes when committed to Git
4. **Convergence**: System continuously working to ensure desired state matches actual state
5. **Pull-Based Deployments**: Cluster pulls changes rather than being pushed to

### Repository Structure

The team organized their GitOps repositories as follows:

```text
├── infrastructure/
│   ├── terraform/
│   │   ├── eks/
│   │   ├── networking/
│   │   ├── databases/
│   │   └── monitoring/
│   └── kubernetes/
│       ├── base/
│       │   ├── namespaces/
│       │   ├── rbac/
│       │   ├── crds/
│       │   └── operators/
│       └── environments/
│           ├── dev/
│           ├── staging/
│           └── production/
├── applications/
│   ├── user-management/
│   ├── authentication/
│   ├── inventory/
│   ├── order-processing/
│   └── ...
└── platform/
    ├── observability/
    ├── security/
    ├── networking/
    └── ci-cd/
```

#### ADR-007: Repository Structure

**Status**: Accepted

**Context**:
We need to establish a repository structure that supports our GitOps workflow, enables team autonomy, and maintains clear separation of concerns between infrastructure, platform components, and applications.

**Decision**:
We will organize our repositories into three main categories: infrastructure, applications, and platform, with a structured hierarchy within each.

**Rationale**:

- Clear separation between infrastructure, platform, and application concerns
- Enables different access controls and approval workflows for different types of changes
- Supports our multi-environment deployment strategy
- Aligns with GitOps principles of declarative configuration
- Facilitates team autonomy while maintaining governance
- Provides a scalable structure as we add more services

**Alternatives Considered**:

1. **Monorepo Approach**:
   - Simpler initial setup
   - Better visibility across all components
   - More complex access control
   - Potential scalability issues as the codebase grows

2. **Service-per-Repository**:
   - Maximum team autonomy
   - Clear ownership boundaries
   - More difficult to maintain consistency
   - Challenges with cross-cutting changes

3. **Environment-per-Repository**:
   - Clear separation between environments
   - Simpler promotion between environments
   - Duplication of configuration
   - Less alignment with GitOps tools

**Consequences**:

- Will need to establish clear ownership boundaries
- Will need tooling to manage changes across repositories
- Will gain better separation of concerns
- Can implement different approval workflows for different types of changes
- Will need to document repository structure and conventions

### Flux Configuration

The team implemented Flux v2 for GitOps automation:

```yaml
# Example Flux GitRepository configuration
apiVersion: source.toolkit.fluxcd.io/v1beta1
kind: GitRepository
metadata:
  name: infrastructure
  namespace: flux-system
spec:
  interval: 1m
  url: https://github.com/technova/infrastructure
  ref:
    branch: main
  secretRef:
    name: github-credentials
---
# Example Flux Kustomization configuration
apiVersion: kustomize.toolkit.fluxcd.io/v1beta1
kind: Kustomization
metadata:
  name: infrastructure
  namespace: flux-system
spec:
  interval: 10m
  path: "./kubernetes/environments/production"
  prune: true
  sourceRef:
    kind: GitRepository
    name: infrastructure
  validation: client
  healthChecks:
    - apiVersion: apps/v1
      kind: Deployment
      name: ingress-nginx-controller
      namespace: ingress-nginx
```

### Deployment Strategy

The team implemented a progressive delivery approach using Flagger for canary deployments:

```yaml
# Example Canary deployment with Flagger
apiVersion: flagger.app/v1beta1
kind: Canary
metadata:
  name: order-processing
  namespace: production
spec:
  provider: appmesh
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: order-processing
  progressDeadlineSeconds: 600
  service:
    port: 80
    targetPort: 8080
    meshName: technova-mesh
  analysis:
    interval: 30s
    threshold: 10
    maxWeight: 50
    stepWeight: 5
    metrics:
      - name: request-success-rate
        threshold: 99
        interval: 1m
      - name: request-duration
        threshold: 500
        interval: 1m
    webhooks:
      - name: load-test
        url: http://flagger-loadtester.test/
        timeout: 5s
        metadata:
          cmd: "hey -z 1m -q 10 -c 2 http://order-processing.production:80/"
```

## CI/CD Pipeline Implementation

### CI Pipeline with GitHub Actions

```yaml
# Example GitHub Actions workflow for CI
name: CI Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2

      - name: Set up JDK
        uses: actions/setup-java@v2
        with:
          java-version: '17'
          distribution: 'adopt'

      - name: Run tests
        run: ./gradlew test

      - name: Run code analysis
        run: ./gradlew sonarqube

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Login to ECR
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build and push
        uses: docker/build-push-action@v2
        with:
          context: .
          push: true
          tags: ${{ secrets.ECR_REPOSITORY }}:${{ github.sha }}

  update-manifests:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        with:
          repository: technova/applications
          token: ${{ secrets.GH_PAT }}

      - name: Update image tag
        run: |
          cd ${{ github.event.repository.name }}
          kustomize edit set image ${{ secrets.ECR_REPOSITORY }}:${{ github.sha }}

      - name: Commit changes
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "TechNova CI"
          git commit -am "Update ${{ github.event.repository.name }} image to ${{ github.sha }}"
          git push
```

### CD with Flux

Flux continuously monitors the Git repositories and automatically applies changes to the Kubernetes clusters:

```yaml
# Example HelmRelease managed by Flux
apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: order-processing
  namespace: production
spec:
  interval: 5m
  chart:
    spec:
      chart: ./charts/microservice
      sourceRef:
        kind: GitRepository
        name: applications
        namespace: flux-system
  values:
    replicaCount: 3
    image:
      repository: 123456789012.dkr.ecr.us-west-2.amazonaws.com/order-processing
      tag: 1.2.3
    resources:
      limits:
        cpu: 1000m
        memory: 1Gi
      requests:
        cpu: 500m
        memory: 512Mi
    autoscaling:
      enabled: true
      minReplicas: 3
      maxReplicas: 10
      targetCPUUtilizationPercentage: 80
    ingress:
      enabled: true
      annotations:
        kubernetes.io/ingress.class: alb
      hosts:
        - host: api.technova.com
          paths:
            - path: /orders
              pathType: Prefix
```

## Microservices Implementation

### Service Communication Patterns

The team implemented a combination of synchronous and asynchronous communication patterns:

```mermaid
graph TD
    A[API Gateway] --> B[Authentication Service]
    A --> C[User Management Service]
    A --> D[Order Processing Service]
    A --> E[Inventory Service]

    D -- "Synchronous (REST)" --> B
    D -- "Synchronous (REST)" --> C
    D -- "Synchronous (REST)" --> E

    D -- "Asynchronous (Event)" --> F[SNS Topic: Orders]

    F --> G[SQS Queue: Fulfillment]
    F --> H[SQS Queue: Invoicing]
    F --> I[SQS Queue: Analytics]

    J[Fulfillment Service] -- "Poll" --> G
    K[Invoicing Service] -- "Poll" --> H
    L[Analytics Service] -- "Poll" --> I
```

### Database Strategy

The team adopted a database-per-service pattern with a mix of database technologies:

| Service | Database Type | Justification |
|---------|--------------|---------------|
| User Management | PostgreSQL | Complex relationships, ACID requirements |
| Authentication | DynamoDB | High read performance, simple data model |
| Inventory | PostgreSQL | Transactional requirements, complex queries |
| Order Processing | PostgreSQL | Transactional integrity, relational data |
| Fulfillment | DynamoDB | Simple data model, high write throughput |
| Analytics | Amazon Redshift | Data warehousing, complex analytics |
| Search | Elasticsearch | Full-text search capabilities |

#### ADR-008: Database Strategy

**Status**: Accepted

**Context**:
As we decompose our monolithic application into microservices, we need to determine the appropriate database strategy that supports service autonomy while ensuring data integrity, performance, and operational efficiency.

**Decision**:
We will adopt a database-per-service pattern with a mix of database technologies selected based on each service's specific data requirements.

**Rationale**:

- Enables independent scaling of databases based on service needs
- Allows each service to use the most appropriate database technology
- Supports service autonomy and independent deployment
- Reduces coupling between services
- Aligns with microservices principles of decentralized data management
- Enables targeted optimization for different data access patterns
- Improves fault isolation between services

**Alternatives Considered**:

1. **Shared Database**:
   - Simpler data consistency and transactions
   - Easier reporting and analytics
   - Creates tight coupling between services
   - Single point of failure
   - Limits technology choices

2. **Command Query Responsibility Segregation (CQRS)**:
   - Optimized read and write models
   - Better performance for complex queries
   - Significantly higher complexity
   - Eventually consistent data
   - Higher development and operational overhead

3. **Hybrid Approach (Shared Database for Some Services)**:
   - Pragmatic balance for closely related services
   - Simpler transactions for related services
   - Still creates coupling between services
   - Complicates service boundaries

**Consequences**:

- Will need to implement distributed transaction patterns (Saga)
- Will need to manage multiple database technologies
- Will need to implement data synchronization mechanisms
- Will gain better service autonomy and scalability
- May face data consistency challenges
- Will need to implement cross-service reporting solutions

### API Design

The team adopted a REST API design with the following principles:

1. **Resource-oriented**: APIs designed around resources
2. **Standard HTTP methods**: Using GET, POST, PUT, DELETE appropriately
3. **Consistent error handling**: Standardized error responses
4. **Versioning**: API versioning in the URL path
5. **Pagination**: Consistent pagination for list endpoints
6. **Filtering**: Standard query parameters for filtering
7. **Documentation**: OpenAPI/Swagger documentation for all APIs

Example API specification:

```yaml
openapi: 3.0.0
info:
  title: Order Processing API
  version: 1.0.0
  description: API for managing orders
paths:
  /v1/orders:
    get:
      summary: List orders
      parameters:
        - name: status
          in: query
          schema:
            type: string
            enum: [pending, processing, completed, cancelled]
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: List of orders
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Order'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
    post:
      summary: Create a new order
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderCreate'
      responses:
        '201':
          description: Order created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Order'
```

## Observability Implementation

### Monitoring Architecture

The team implemented a comprehensive monitoring solution:

```mermaid
graph TD
    subgraph "Kubernetes Cluster"
        A[Application Pods]
        B[Prometheus Operator]
        C[Prometheus Server]
        D[Alertmanager]
        E[Grafana]
        F[Fluent Bit DaemonSet]
    end

    subgraph "AWS Services"
        G[CloudWatch Logs]
        H[Elasticsearch Service]
        I[S3 Bucket]
        J[SNS Topic]
    end

    A -- "Metrics" --> C
    B -- "Manages" --> C
    B -- "Manages" --> D
    C -- "Alerts" --> D
    C -- "Dashboards" --> E
    D -- "Notifications" --> J

    A -- "Logs" --> F
    F -- "Streams" --> G
    F -- "Streams" --> H
    H -- "Archives" --> I
```

#### ADR-009: Monitoring Architecture

**Status**: Accepted

**Context**:
With a distributed microservices architecture deployed on Kubernetes, we need a comprehensive monitoring solution that provides visibility into application performance, infrastructure health, and business metrics while supporting our operational requirements.

**Decision**:
We will implement a monitoring architecture based on Prometheus, Grafana, Fluent Bit, and Elasticsearch, with integration to AWS services for long-term storage and notifications.

**Rationale**:

- Prometheus provides a powerful metrics collection system designed for Kubernetes
- Grafana offers flexible visualization and dashboarding capabilities
- Fluent Bit efficiently collects and forwards logs with minimal resource usage
- Elasticsearch enables powerful log search and analysis
- AWS integration provides scalable storage and notification capabilities
- Open-source components reduce vendor lock-in
- Architecture supports both real-time monitoring and historical analysis
- Components can be deployed within our Kubernetes clusters

**Alternatives Considered**:

1. **AWS-native Monitoring (CloudWatch)**:
   - Tighter AWS integration
   - Managed service with less operational overhead
   - Higher cost at our scale
   - Less flexibility for custom metrics
   - Less Kubernetes-native

2. **Commercial Observability Platforms (Datadog, New Relic)**:
   - Comprehensive out-of-the-box capabilities
   - Simplified setup and management
   - Significantly higher cost
   - Potential vendor lock-in
   - Less control over data retention

3. **ELK Stack (Elasticsearch, Logstash, Kibana)**:
   - More comprehensive log analysis
   - Higher resource requirements
   - More complex to operate
   - Less focused on metrics collection

**Consequences**:

- Will need to deploy and manage monitoring components in our clusters
- Will need to establish standards for metrics and logging
- Will gain comprehensive visibility into our distributed system
- Can implement advanced alerting and dashboarding
- Will need to manage storage and retention policies
- Will need to train team on effective use of monitoring tools

### Metrics Collection

The team standardized on Prometheus metrics with the following categories:

1. **Business Metrics**: Order volume, conversion rates, user activity
2. **Application Metrics**: Request rates, error rates, latencies
3. **Runtime Metrics**: JVM metrics, memory usage, garbage collection
4. **Infrastructure Metrics**: CPU, memory, disk, network
5. **Kubernetes Metrics**: Pod status, node health, deployment status

Example Prometheus configuration:

```yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: order-processing
  namespace: monitoring
  labels:
    app: order-processing
spec:
  selector:
    matchLabels:
      app: order-processing
  endpoints:
  - port: metrics
    interval: 15s
    path: /actuator/prometheus
  namespaceSelector:
    matchNames:
    - production
```

### Logging Strategy

The team implemented structured logging with the following components:

1. **Log Format**: JSON-structured logs with standardized fields
2. **Collection**: Fluent Bit DaemonSet on each node
3. **Storage**: Elasticsearch for searchable logs, S3 for long-term archival
4. **Retention**: 7 days in Elasticsearch, 1 year in S3
5. **Context Propagation**: Distributed tracing IDs included in logs

Example Fluent Bit configuration:

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluent-bit-config
  namespace: logging
data:
  fluent-bit.conf: |
    [SERVICE]
        Flush         1
        Log_Level     info
        Daemon        off
        Parsers_File  parsers.conf

    [INPUT]
        Name              tail
        Tag               kube.*
        Path              /var/log/containers/*.log
        Parser            docker
        DB                /var/log/flb_kube.db
        Mem_Buf_Limit     5MB
        Skip_Long_Lines   On
        Refresh_Interval  10

    [FILTER]
        Name                kubernetes
        Match               kube.*
        Kube_URL            https://kubernetes.default.svc:443
        Kube_CA_File        /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        Kube_Token_File     /var/run/secrets/kubernetes.io/serviceaccount/token
        Merge_Log           On
        K8S-Logging.Parser  On
        K8S-Logging.Exclude Off

    [OUTPUT]
        Name            es
        Match           *
        Host            ${ELASTICSEARCH_HOST}
        Port            ${ELASTICSEARCH_PORT}
        Logstash_Format On
        Logstash_Prefix technova
        Time_Key        @timestamp
        Type            _doc
        Replace_Dots    On
        Retry_Limit     False

    [OUTPUT]
        Name                          s3
        Match                         *
        bucket                        technova-logs
        region                        us-west-2
        s3_key_format                 /year=%Y/month=%m/day=%d/hour=%H/$TAG/%Y%m%d%H%M%S_$UUID.gz
        s3_key_format_tag_delimiters  .-
        use_put_object                On
```

### Alerting Configuration

The team implemented a multi-level alerting strategy:

1. **Severity Levels**: Critical, Warning, Info
2. **Alert Routing**: Different channels based on severity and service
3. **On-Call Rotation**: PagerDuty integration for critical alerts
4. **Business Hours**: Slack for non-critical alerts during business hours
5. **Runbooks**: Links to runbooks included in alerts

Example Alertmanager configuration:

```yaml
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: order-processing-alerts
  namespace: monitoring
spec:
  groups:
  - name: order-processing
    rules:
    - alert: HighErrorRate
      expr: sum(rate(http_server_requests_seconds_count{status=~"5..",service="order-processing"}[5m])) / sum(rate(http_server_requests_seconds_count{service="order-processing"}[5m])) > 0.05
      for: 5m
      labels:
        severity: critical
        service: order-processing
      annotations:
        summary: "High error rate on Order Processing service"
        description: "Error rate is above 5% for more than 5 minutes"
        runbook_url: "https://wiki.technova.com/runbooks/order-processing/high-error-rate"
```

## Results and Outcomes

### Performance Improvements

| Metric | Before | After | Improvement |
|--------|--------|-------|-------------|
| Deployment Frequency | Bi-weekly | Multiple times per day | 10x |
| Lead Time for Changes | 1-2 weeks | < 1 day | 90% reduction |
| Mean Time to Recovery | 4 hours | 15 minutes | 94% reduction |
| Change Failure Rate | 20% | 5% | 75% reduction |
| System Availability | 99.5% | 99.99% | 10x fewer outages |
| Average Response Time | 800ms | 200ms | 75% reduction |
| Peak Load Capacity | 1,000 req/s | 10,000 req/s | 10x increase |

### Business Impact

1. **Faster Time to Market**: New features delivered in days instead of weeks
2. **Improved Reliability**: 10x reduction in production incidents
3. **Better Scalability**: Handled 3x customer growth without performance issues
4. **Cost Efficiency**: 30% reduction in infrastructure costs through better resource utilization
5. **Developer Productivity**: 40% increase in developer productivity
6. **Customer Satisfaction**: NPS score improved from 35 to 72

### Challenges Encountered

1. **Microservices Complexity**: Managing the increased operational complexity of distributed systems
2. **Data Consistency**: Ensuring data consistency across microservices
3. **Monitoring Overhead**: Setting up comprehensive monitoring for all services
4. **Skills Gap**: Training team members on Kubernetes and cloud-native technologies
5. **Migration Strategy**: Determining the right approach for migrating from monolith to microservices
6. **Service Boundaries**: Defining appropriate service boundaries and responsibilities

### Solutions Implemented

1. **Service Mesh**: Implemented AWS App Mesh for network traffic management
2. **Saga Pattern**: Used choreography-based sagas for distributed transactions
3. **Observability Platform**: Comprehensive monitoring with Prometheus, Grafana, and Elasticsearch
4. **Training Program**: Structured training on Kubernetes, GitOps, and cloud-native patterns
5. **Strangler Pattern**: Incremental migration using the strangler pattern
6. **Domain-Driven Design**: Used DDD to identify bounded contexts and service boundaries

## Lessons Learned

### What Worked Well

1. **GitOps Approach**: Declarative configuration and automated deployments improved reliability
2. **Progressive Delivery**: Canary deployments reduced the risk of changes
3. **Infrastructure as Code**: Terraform and Kubernetes manifests provided consistency
4. **Observability First**: Early investment in monitoring paid off during troubleshooting
5. **Cross-Functional Teams**: Aligning teams around business domains improved ownership
6. **Automated Testing**: Comprehensive test automation improved quality

### What Could Be Improved

1. **Initial Complexity**: Started with too many microservices; should have been more incremental
2. **Local Development**: Developer experience for local development was initially challenging
3. **Documentation**: Better documentation needed for cross-service dependencies
4. **Performance Testing**: More comprehensive performance testing earlier in the process
5. **Cost Monitoring**: Earlier implementation of cost monitoring and optimization
6. **Knowledge Sharing**: More structured approach to knowledge sharing across teams

### Key Recommendations

1. **Start Small**: Begin with a few well-defined microservices and expand gradually
2. **Invest in Platform**: Build a solid platform team and tooling before scaling microservices
3. **Standardize Patterns**: Establish standard patterns for common challenges
4. **Automate Everything**: Invest in automation for testing, deployment, and operations
5. **Measure Impact**: Define clear metrics to measure the impact of the transformation
6. **Focus on Developer Experience**: Make it easy for developers to build, test, and deploy

## Learning Outcomes

After studying this case study, you should be able to:

1. Design a microservices architecture using domain-driven design principles
2. Implement GitOps workflows for Kubernetes using Flux
3. Configure and manage AWS EKS for production workloads
4. Design effective CI/CD pipelines for microservices
5. Implement comprehensive observability for distributed systems
6. Apply progressive delivery techniques like canary deployments
7. Understand common challenges in microservices migrations and how to address them
8. Evaluate the business impact of DevOps transformations
9. Apply infrastructure as code practices for cloud resources
10. Design effective team structures for microservices development
